{"name": "esto-partner-cra", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "3.9.8", "@chakra-ui/accordion": "^2.3.1", "@chakra-ui/alert-dialog": "1.0.0-next.3", "@chakra-ui/avatar": "^2.3.0", "@chakra-ui/checkbox": "^2.3.2", "@chakra-ui/form-control": "^2.2.0", "@chakra-ui/input": "^2.1.2", "@chakra-ui/layout": "^2.3.1", "@chakra-ui/modal": "^2.3.1", "@chakra-ui/popover": "^2.2.1", "@chakra-ui/popper": "^3.1.0", "@chakra-ui/react": "3.21.1", "@chakra-ui/spinner": "^2.1.0", "@chakra-ui/switch": "^2.1.2", "@chakra-ui/system": "^2.6.2", "@chakra-ui/table": "^2.1.0", "@chakra-ui/tabs": "^3.0.0", "@chakra-ui/theme": "^3.4.6", "@chakra-ui/theme-tools": "2.2.6", "@chakra-ui/toast": "^7.0.2", "@chakra-ui/tooltip": "^2.3.1", "@chakra-ui/transition": "^2.1.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.1", "@hookform/resolvers": "3.3.4", "@sentry/cli": "2.30.2", "@sentry/react": "7.108.0", "compose-function": "3.0.3", "copy-to-clipboard": "3.3.3", "date-fns": "4.1.0", "downshift": "9.0.8", "effector": "23.2.0", "effector-react": "23.2.0", "file-saver": "2.0.5", "framer-motion": "12.23.0", "graphql": "16.8.1", "history": "5.3.0", "i18next": "23.10.1", "i18next-browser-languagedetector": "7.2.0", "i18next-locize-backend": "6.4.1", "lodash": "4.17.21", "lottie-web": "5.12.2", "posthog-js": "1.249.5", "qrcode.react": "3.1.0", "query-string": "9.0.0", "react": "19.1.0", "react-color": "2.19.3", "react-day-picker": "8.10.0", "react-dom": "19.1.0", "react-ga4": "^2.1.0", "react-hook-form": "7.60.0", "react-i18next": "14.1.0", "react-icons": "5.0.1", "react-number-format": "5.3.4", "react-router-dom": "6.22.3", "web-vitals": "3.5.2", "zod": "3.25.75"}, "scripts": {"start": "env-cmd -e local vite", "start:dekker": "env-cmd -e dekker vite", "start:dekker-dev1": "env-cmd -e dekker-dev1 vite", "serve": "env-cmd -e local vite", "serve:domain": "HTTPS=true HOST=affiliate.esto.test SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e local vite", "serve:domain-dekker": "HTTPS=true HOST=affiliate.dekker.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker vite", "serve:domain-dev0": "HTTPS=true HOST=affiliate.dekker-dev0.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev0 vite", "serve:domain-dev1": "HTTPS=true HOST=affiliate.dekker-dev1.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev1 vite", "serve:domain-dev2": "HTTPS=true HOST=affiliate.dekker-dev2.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev2 vite", "serve:domain-dev3": "HTTPS=true HOST=affiliate.dekker-dev3.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev3 vite", "certificate:mac": "sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain affiliate.esto.test.crt", "build": "vite build", "build:dekker": "env-cmd -e dekker-dev2 vite build", "build:dekker-dev1": "env-cmd -e dekker-dev1 vite build", "test": "vitest", "cypress:open": "env-cmd -e dekker-dev2 cypress open", "cypress:run": "env-cmd -e dekker-dev2 cypress run --browser chrome --component", "upload-src-maps": "node scripts/upload-src-maps.js", "print-version": "node scripts/print-version.js", "codegen": "env-cmd -e dekker-dev1 graphql-codegen --config codegen.ts", "codegen:watch": "env-cmd -e dekker-dev1 graphql-codegen --config codegen.ts --watch", "analyze": "vite build --mode analyze", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "lint-and-format": "pnpm run lint:fix && pnpm run format", "ts-check": "tsc --noEmit", "git-hooks-install": "lefthook install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@faker-js/faker": "8.4.1", "@graphql-codegen/add": "5.0.2", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/near-operation-file-preset": "3.0.0", "@graphql-codegen/typescript": "4.0.6", "@graphql-codegen/typescript-graphql-request": "6.2.0", "@graphql-codegen/typescript-operations": "4.2.0", "@graphql-codegen/typescript-react-apollo": "4.3.0", "@parcel/watcher": "2.4.1", "@tanstack/eslint-plugin-query": "^5.78.0", "@types/compose-function": "0.0.33", "@types/file-saver": "2.0.7", "@types/lodash": "4.17.0", "@types/node": "24.0.10", "@types/qrcode.react": "1.0.5", "@types/react": "18.2.67", "@types/react-color": "3.0.12", "@types/react-dom": "18.2.22", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.5.2", "cypress": "13.7.1", "env-cmd": "10.1.0", "eslint": "9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^15.15.0", "lefthook": "1.6.7", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^6.0.3", "typescript": "4.9.5", "vite": "^7.0.2", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.2.3"}}