import { Box, Icon, IconButton, SimpleGrid, Tabs } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FiArrowLeft } from 'react-icons/fi';
import { Link, Outlet, useNavigate } from 'react-router-dom';
import { Header } from 'shared/components';
import { DevToolsTabRoute, ForDeveloperRoute } from 'shared/constants/routes';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { DevToolsRouteTabs } from 'shared/types';

import { useDevToolsTabByPathname } from './DevToolsLayout.hooks';

const TABS = [
  DevToolsRouteTabs.Setup,
  DevToolsRouteTabs.Urls,
  DevToolsRouteTabs.Calculator,
];

export const DevToolsLayout = () => {
  const { t } = useTranslation('dev-tools');
  const tab = useDevToolsTabByPathname();
  const navigate = useNavigate();
  const isMobileLayout = useIsMobileLayout();

  const renderHeader = () => {
    if (isMobileLayout) {
      return (
        <Header
          before={
            <Link to={ForDeveloperRoute.format()}>
              <IconButton aria-label="back" mx={2} my={1} variant="ghost">
                <Icon as={FiArrowLeft} boxSize={6} color="primary.800" />
              </IconButton>
            </Link>
          }
          isSidebarButtonEnabled={false}
          title={t(`tabs.${tab}`)}
        />
      );
    }

    return (
      <Tabs.Root
        align="center"
        bg="white"
        value={tab}
        onValueChange={(details: { value: string }) => {
          const index = TABS.indexOf(details.value as DevToolsRouteTabs);
          if (index !== -1) {
            navigate(DevToolsTabRoute.format({ tab: TABS[index] }));
          }
        }}
        position="sticky"
        top={0}
        width="100%"
        zIndex={2}
        left={0}
      >
        <Tabs.List>
          {TABS.map((tabValue) => (
            <Tabs.Trigger key={tabValue} value={tabValue}>
              {t(`tabs.${tabValue}`)}
            </Tabs.Trigger>
          ))}
        </Tabs.List>
      </Tabs.Root>
    );
  };

  return (
    <SimpleGrid gridTemplateRows="auto 1fr">
      {renderHeader()}
      <Box px="20px">
        <Outlet />
      </Box>
    </SimpleGrid>
  );
};
