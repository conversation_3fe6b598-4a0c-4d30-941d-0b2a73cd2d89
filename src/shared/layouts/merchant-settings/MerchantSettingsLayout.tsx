import { Box, Icon, IconButton, SimpleGrid, Tabs } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FiArrowLeft } from 'react-icons/fi';
import { Link, Outlet, useNavigate } from 'react-router-dom';
import { CountBadge, Header } from 'shared/components';
import { SettingsRoute, SettingsTabRoute } from 'shared/constants/routes';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { SettingsRouteTabs } from 'shared/types';

import { useMerchantSettingsTabDataByPathname } from './MerchantSettingsLayout.hooks';

const TABS = [
  SettingsRouteTabs.Merchant,
  SettingsRouteTabs.Payment,
  SettingsRouteTabs.Stores,
  SettingsRouteTabs.Employees,
];

export const MerchantSettingsLayout = () => {
  const isMobileLayout = useIsMobileLayout();
  const navigate = useNavigate();
  const { t } = useTranslation('settings');
  const { tab, count, actionIcon, onAction, title } =
    useMerchantSettingsTabDataByPathname();

  const renderHeader = () => {
    if (isMobileLayout) {
      return (
        <Header
          after={
            <>
              {!!count && <CountBadge count={count} />}
              {!!(actionIcon && onAction) && (
                <IconButton
                  aria-label="back"
                  ml="auto"
                  mr={2}
                  onClick={onAction}
                  variant="ghost"
                >
                  <Icon as={actionIcon} boxSize={6} color="primary.800" />
                </IconButton>
              )}
            </>
          }
          before={
            <Link to={SettingsRoute.format()}>
              <IconButton aria-label="back" mx={2} my={1} variant="ghost">
                <Icon as={FiArrowLeft} boxSize={6} color="primary.800" />
              </IconButton>
            </Link>
          }
          isSidebarButtonEnabled={false}
          title={t(title || `tabs.${tab}`)}
        />
      );
    }

    return (
      <Tabs.Root
        align="center"
        bg="white"
        value={tab}
        onValueChange={(details) => {
          const index = TABS.indexOf(details.value as SettingsRouteTabs);
          if (index !== -1) {
            navigate(SettingsTabRoute.format({ tab: TABS[index] }));
          }
        }}
        position="sticky"
        top={0}
        width="100%"
        zIndex={2}
        left={0}
      >
        <Tabs.List>
          {TABS.map((tabValue) => (
            <Tabs.Trigger key={tabValue} value={tabValue}>
              {t(`tabs.${tabValue}`)}
            </Tabs.Trigger>
          ))}
        </Tabs.List>
      </Tabs.Root>
    );
  };

  return (
    <SimpleGrid gridTemplateRows="auto 1fr">
      {renderHeader()}
      <Box overflowX="hidden" px="20px">
        <Outlet />
      </Box>
    </SimpleGrid>
  );
};
