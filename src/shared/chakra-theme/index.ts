import { createSystem, defaultConfig, defineConfig } from '@chakra-ui/react';

import type { ThemeConfig } from './config';
import getFoundations from './foundations';
import { textStyles } from './foundations/typography';
import colors from './foundations/colors';

export const getSystem = (config: ThemeConfig) => {
  const foundations = getFoundations(config);

  const customConfig = defineConfig({
    globalCss: {
      '#root': {
        height: '100vh',
        overflow: 'hidden',
      },
      html: {
        height: '100%',
      },
      body: {
        height: '100%',
        fontFamily: foundations.fonts.karla,
        color: 'neutral.must',
        bg: 'white',
        '*': {
          WebkitTapHighlightColor: 'transparent',
        },
      },
    },
    theme: {
      tokens: {
        fonts: {
          karla: { value: foundations.fonts.karla },
        },
        colors: Object.fromEntries(
          Object.entries(colors).map(([key, colorScale]) => [
            key,
            Object.fromEntries(
              Object.entries(colorScale).map(([shade, value]) => [
                shade,
                { value },
              ]),
            ),
          ]),
        ),
      },
      textStyles: Object.fromEntries(
        Object.entries(textStyles).map(([key, style]) => [
          key,
          { value: style },
        ]),
      ),
    },
  });

  return createSystem(defaultConfig, customConfig);
};
