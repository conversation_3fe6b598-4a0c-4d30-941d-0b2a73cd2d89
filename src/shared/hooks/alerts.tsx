import { useToast } from '@chakra-ui/toast';
import { captureException, captureMessage } from '@sentry/react';
import { type ElementType, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FiAlertCircle, FiCheck } from 'react-icons/fi';
import { Alert, AlertType } from 'shared/components/Alert';
import { extractGraphqlErrors, isUnauthorizedError } from 'shared/utils';

type ShowAlertOptions = {
  message: string;
  icon: ElementType;
  type: AlertType;
  callToAction?: () => void;
  callToActionLabel?: string;
};

// Define types for error objects that come from the backend
type BackendError = {
  code?: number;
  validation?: Record<string, string[]>;
};

function useShowAlert() {
  const toast = useToast();
  const id = useRef<number>(0);

  return useCallback(
    ({
      message,
      icon,
      type,
      callToAction,
      callToActionLabel,
    }: ShowAlertOptions) => {
      toast({
        id: `alert-${id.current}-${type}`,
        position: 'bottom',
        duration: 5000,
        render({ id }) {
          return callToAction ? (
            <Alert
              callToActionLabel={callToActionLabel}
              data-cy="alert"
              icon={icon}
              id={id?.toString() || ''}
              message={message}
              onCallToAction={() => {
                callToAction();
              }}
              type={type}
            />
          ) : (
            <Alert
              data-cy="alert"
              icon={icon}
              id={id?.toString() || ''}
              message={message}
              type={type}
            />
          );
        },
      });

      id.current++;
    },
    [toast],
  );
}

/*
 * This will show red toast with message provided.
 * Should be used if you parse any errors in place, and want to show them
 */
export function useShowError() {
  const showAlert = useShowAlert();

  return useCallback(
    (errorMessage: string) => {
      showAlert({
        message: errorMessage,
        type: AlertType.ERROR,
        icon: FiAlertCircle,
      });
    },
    [showAlert],
  );
}

/*
 * This will show black toast with message.
 * Should be used to display various notifications like "Something was done, thank you"
 */
export function useShowMessage() {
  const showAlert = useShowAlert();

  return useCallback(
    (message: string, icon: ElementType = FiCheck) => {
      showAlert({ message, type: AlertType.MESSAGE, icon });
    },
    [showAlert],
  );
}
/*
 * This will show black toast with message and call to action button.
 * Should be used to display various notifications like "Something was done, thank you"
 */
export function useShowMessageWithCallToActionElement() {
  const showAlert = useShowAlert();

  return useCallback(
    ({
      message,
      icon = FiCheck,
      callToAction,
      callToActionLabel,
    }: {
      message: string;
      icon?: ElementType;
      callToAction?: () => void;
      callToActionLabel?: string;
    }) => {
      showAlert({
        message,
        type: AlertType.MESSAGE,
        icon,
        callToAction,
        callToActionLabel,
      });
    },
    [showAlert],
  );
}

function hasMessage(error: unknown): error is { message: string } {
  return (
    typeof error === 'object' &&
    error !== null &&
    typeof (error as { message: unknown }).message === 'string'
  );
}

function useHandleUnknownError() {
  const showError = useShowError();
  const { t } = useTranslation();

  return useCallback(
    (error?: unknown) => {
      showError(t('errors.unknown'));

      const context = {
        tags: { source: 'handleUnknownError' },
      };

      if (typeof error === 'string') {
        captureMessage(error, context);
      } else if (typeof error === 'object') {
        if (error instanceof Error) {
          captureException(error, context);
        } else {
          let message = 'Unknown error';
          if (hasMessage(error)) {
            message = error.message;
          }

          captureMessage(message, {
            ...context,
            extra: {
              originalError: error,
            },
          });
        }
      }
    },
    [showError, t],
  );
}

function useHandleCodeError() {
  const showError = useShowError();
  const { t } = useTranslation();

  return useCallback(
    (code: number) => {
      const translationKey = `errors.${code}`;
      const message = t(translationKey, {
        defaultValue: t('errors.unknown-code', { code }),
      });
      showError(message);
    },
    [showError, t],
  );
}

function useHandleValidationError() {
  const showError = useShowError();
  const { t } = useTranslation();

  return useCallback(
    ({
      errorType,
      errorTransKey,
    }: {
      errorType: string;
      errorTransKey: string;
    }) => {
      showError(t(`errors.${errorType}.${errorTransKey}`));
    },
    [showError, t],
  );
}

/*
 * Should be used to handle most of errors.
 * This method will try to:
 *   - extract any errors that have unique code from backend, and translate them using "common.errors"
 *   - fallback to "Unknown error" message if it fails to extract any meaningful message
 *   - feel free to add any common errors here as well...
 */
export function useHandleGenericError() {
  const handleUnknownError = useHandleUnknownError();
  const handleCodeError = useHandleCodeError();
  const handleValidationError = useHandleValidationError();

  return useCallback(
    (error: unknown) => {
      const [graphqlErrors] = extractGraphqlErrors(error);

      if (graphqlErrors.length > 0) {
        // Process any graphql errors
        // graphqlErrors.forEach((err) => {

        // });
        for (const err of graphqlErrors) {
          // Don't show unauthorized errors, as we catch every of them, and redirect to login
          if (isUnauthorizedError(err)) {
            return;
          }

          // Backend returns 'code' field for most of the errors
          const backendErr = err as BackendError;
          if (typeof backendErr.code === 'number') {
            handleCodeError(backendErr.code);
          } else if (typeof backendErr.validation === 'object') {
            // Backend returns 'validation' errors as an object, here we handle each validation error and show err message
            for (const [errorType, errorTransKeyArr] of Object.entries(
              backendErr.validation,
            )) {
              handleValidationError({
                errorType,
                errorTransKey: errorTransKeyArr[0],
              });
            }
          } else {
            // Some errors don't have 'code' field, so process them in generic way
            handleUnknownError(error);
          }
        }
      } else {
        // If this is not graphql error, process it in generic way
        handleUnknownError(error);
      }
    },
    [handleUnknownError, handleCodeError, handleValidationError],
  );
}
