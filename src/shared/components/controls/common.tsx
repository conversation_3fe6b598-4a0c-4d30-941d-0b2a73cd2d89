import { type BoxProps, Field, Text } from '@chakra-ui/react';
import type { PropsWithChildren } from 'react';

export type SelectItemType<VType = unknown> = {
  value: VType;
  label: string;
};
export type BaseInputProps = {
  label?: React.ReactNode;
  subText?: string;
  name?: string;
  error?: string;
  hint?: React.ReactNode;
  extraState?: React.ReactNode;
  isDisabled?: boolean;
} & Omit<BoxProps, 'onChange'>;

export const InputWrapper = ({
  name,
  label,
  subText,
  isDisabled,
  error,
  hint,
  extraState,
  children,
  ...chakraProps
}: PropsWithChildren<BaseInputProps>): JSX.Element => (
  <Field.Root
    id={`${name}-input`}
    {...chakraProps}
    disabled={isDisabled}
    invalid={!!error}
    pb={hint || error || extraState ? 0 : '19px'}
  >
    {!!label &&
      (subText ? (
        <Field.Label fontSize="16px" fontWeight="700" mb={[1]}>
          {label}
        </Field.Label>
      ) : (
        <Field.Label>{label}</Field.Label>
      ))}
    {!!subText && (
      <Text fontSize="14px" fontWeight="400" mb={[2]}>
        {subText}
      </Text>
    )}
    {children}
    {!!hint && !error && !extraState && (
      <Field.HelperText>{hint}</Field.HelperText>
    )}
    {!!extraState && !error && (
      <Text
        color="primary.700"
        mt={1}
        textAlign="end"
        textStyle="caption-highlight"
      >
        {extraState}
      </Text>
    )}
    <Field.ErrorText data-cy="error-message">{error}</Field.ErrorText>
  </Field.Root>
);
