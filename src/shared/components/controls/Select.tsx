import {
  Box,
  chakra,
  Field,
  Icon,
  Input,
  Portal,
  Text,
} from '@chakra-ui/react';
import { usePopper } from '@chakra-ui/popper';
import { useSelect } from 'downshift';
import { motion, type Variants } from 'framer-motion';
import type React from 'react';
import { type FC, useCallback, useMemo } from 'react';

// Simple ref merger for Chakra UI v3 compatibility
const mergeRefs = <T,>(refs: Array<React.Ref<T> | undefined>) => {
  return (node: T) => {
    refs.forEach((ref) => {
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref != null) {
        (ref as React.MutableRefObject<T | null>).current = node;
      }
    });
  };
};
import { useController, type UseControllerProps } from 'react-hook-form';
import { FiChevronDown } from 'react-icons/fi';

import type { BaseInputProps, SelectItemType } from './common';

type Placement = 'top' | 'bottom' | 'left' | 'right';

export type SelectProps<ItemType> = {
  items: Array<ItemType>;
  value?: ItemType | null;
  placement?: Placement;
  renderItem?: (
    item: ItemType,
    isSelected?: boolean,
    isInputRendered?: boolean,
  ) => React.ReactNode;
  onChange?: (val: ItemType) => void;
} & BaseInputProps;

const defaultRenderItem = <ItemType extends SelectItemType>(
  item: ItemType,
  isSelected: boolean,
  isInputRendered = false,
): React.ReactNode => (
  <Text
    overflow="hidden"
    textOverflow="ellipsis"
    textStyle={isSelected ? 'body1-highlight' : 'body1'}
    whiteSpace={isInputRendered ? 'nowrap' : undefined}
    width="100%"
  >
    {item.label}
  </Text>
);

const motionVariants: Variants = {
  enter: {
    visibility: 'visible',
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1],
    },
  },
  exit: {
    transitionEnd: {
      visibility: 'hidden',
    },
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.1,
      ease: 'easeOut',
    },
  },
};

const Motion = chakra(motion.div);

export const Select = <ItemType extends SelectItemType>({
  label,
  name,
  items,
  hint,
  isDisabled,
  value,
  placement = 'bottom',
  // @ts-expect-error: downshift types are not compatible with react-hook-form types
  renderItem = defaultRenderItem,
  onChange,
  error,
  extraState,
  ...chackraProps
}: SelectProps<ItemType>) => {
  // downshift hook that handles all dropdown related stuff (event handlers, state, aria attributes getter etc.)
  const {
    isOpen,
    selectedItem,
    getToggleButtonProps,
    getLabelProps,
    getMenuProps,
    highlightedIndex,
    openMenu,
    getItemProps,
  } = useSelect({
    items,
    selectedItem: value,
    itemToString: (item) => item?.label ?? '',
    onSelectedItemChange: ({ selectedItem }) => {
      if (selectedItem) {
        onChange?.(selectedItem);
      }
    },
  });

  // popper hook used to position menu element relative to trigger
  const { popperRef, referenceRef } = usePopper({
    placement,
    matchWidth: true,
    offset: [0, 0],
  });

  // styles applied to input when menu is opened
  const inputStyles = useMemo(
    () =>
      isOpen
        ? {
            px: 'calc(1rem - 1px)',
            border: '2px solid',
            borderColor: 'neutral.900',
          }
        : {},
    [isOpen],
  );

  const openOnEnterPressed = useCallback(
    (e: React.KeyboardEvent<HTMLElement>) => {
      if (e.key === 'Enter') {
        openMenu();
        e.preventDefault();
        e.stopPropagation();
      }
    },
    [openMenu],
  );

  const toggleProps = getToggleButtonProps({
    disabled: isDisabled,
    onKeyDown: openOnEnterPressed,
  });
  const menuProps = getMenuProps({}, { suppressRefError: true });

  return (
    <Box {...chackraProps}>
      <Field.Root
        disabled={isDisabled}
        pb={hint || error || extraState ? 0 : '19px'}
        invalid={!!error}
      >
        {!!label && <Field.Label {...getLabelProps()}>{label}</Field.Label>}
        <Box position="relative">
          <Input
            {...toggleProps}
            cursor="pointer"
            readOnly
            name={name}
            ref={mergeRefs([referenceRef, toggleProps.ref])}
            type="text"
            {...inputStyles}
          />

          {!!selectedItem && (
            <Box
              alignItems="center"
              data-cy="select-value"
              display="flex"
              h={10}
              pl={4}
              pointerEvents="none"
              position="absolute"
              pr={10}
              w="100%"
              zIndex="2"
            >
              {renderItem(selectedItem, false, true)}
            </Box>
          )}

          <Box
            alignItems="center"
            display="flex"
            h={10}
            pointerEvents="none"
            position="absolute"
            right={3}
            top={0}
          >
            <Icon
              as={FiChevronDown}
              boxSize="1.5rem"
              color="neutral.900"
              transform={isOpen ? 'rotate(180deg)' : 'rotate(0)'}
            />
          </Box>
        </Box>
        {!!hint && !error && !extraState && (
          <Field.HelperText>{hint}</Field.HelperText>
        )}
        {!!extraState && !error && (
          <Text
            color="primary.700"
            mt={1}
            textAlign="end"
            textStyle="caption-highlight"
          >
            {extraState}
          </Text>
        )}
        <Field.ErrorText data-cy="error-message">{error}</Field.ErrorText>
      </Field.Root>
      <Portal>
        <Box
          data-cy="select-menu"
          {...menuProps}
          outline="none"
          ref={mergeRefs([popperRef, menuProps.ref])}
          zIndex={isOpen ? 2000 : -1}
        >
          <Motion
            animate={isOpen ? 'enter' : 'exit'}
            bg="white"
            borderRadius="4px"
            boxShadow="0px 1px 8px rgba(0, 0, 0, 0.13), 0px 8px 16px rgba(0, 0, 0, 0.07);"
            initial={false}
            maxHeight="20rem"
            overflow="auto"
            py={2}
            variants={motionVariants}
          >
            {!!open &&
              items.map((item, index) => (
                <Box
                  bg={highlightedIndex === index ? 'neutral.50' : 'transparent'}
                  cursor="pointer"
                  data-cy="select-menu-item"
                  key={item.label}
                  pl={4}
                  pr={10}
                  py={2}
                  {...getItemProps({ item, index })}
                >
                  {renderItem(item, selectedItem === item)}
                </Box>
              ))}
          </Motion>
        </Box>
      </Portal>
    </Box>
  );
};

type ControlledSelectProps<T extends SelectItemType = SelectItemType> = {
  control: UseControllerProps['control'];
} & Omit<SelectProps<T>, 'value' | 'onChange' | 'name'> &
  Pick<UseControllerProps, 'name' | 'rules' | 'control'>;

export const ControlledSelect: FC<ControlledSelectProps> = ({
  name,
  rules,
  control,
  ...selectProps
}) => {
  const { field } = useController({ name, rules, control });
  const value = useMemo(
    () => selectProps.items.find((item) => item.value === field.value),
    [field.value, selectProps.items],
  );

  return (
    <Select
      {...selectProps}
      onChange={({ value }: SelectItemType) => {
        if (field.value !== value) {
          field.onChange(value);
        }
      }}
      value={value as SelectItemType | null | undefined}
    />
  );
};
