import {
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
} from '@chakra-ui/popover';
import { Box, type BoxProps, Icon } from '@chakra-ui/react';
import { type FC, memo, type ReactNode } from 'react';
import { CgInfo } from 'react-icons/cg';

type AppTooltipProps = BoxProps & {
  label: string;
  placement?: AppTooltipPlacements;
  isInfo?: boolean;
  children?: ReactNode;
};

enum AppTooltipPlacements {
  auto = 'auto',
  top = 'top',
  bottom = 'bottom',
  right = 'right',
  left = 'left',
}

export const AppTooltip: FC<AppTooltipProps> = memo(
  ({
    label,
    placement = AppTooltipPlacements.top,
    children,
    isInfo = false,
    ...chakraProps
  }: AppTooltipProps) => {
    const renderPopoverTriggerContent = (): ReactNode =>
      isInfo ? (
        <Icon
          _hover={{ color: 'neutral.must' }}
          as={CgInfo}
          boxSize={5}
          color="primary.800"
        />
      ) : (
        children
      );

    return (
      <Popover placement={placement} trigger="hover">
        <PopoverTrigger>
          <Box cursor="pointer" w="fit-content" {...chakraProps}>
            {renderPopoverTriggerContent()}
          </Box>
        </PopoverTrigger>
        <PopoverContent
          bg="neutral.900"
          color="white"
          maxW="320px"
          p={0}
          w="100%"
        >
          <PopoverArrow bg="neutral.900" />
          <PopoverBody fontSize="12px" lineHeight="16px">
            {label}
          </PopoverBody>
        </PopoverContent>
      </Popover>
    );
  },
);

AppTooltip.displayName = 'AppTooltip';
