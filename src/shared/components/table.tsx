import { type BoxProps } from '@chakra-ui/react';
import { Td as ChakraTd, Th as ChakraTh } from '@chakra-ui/table';
import type { PropsWithChildren } from 'react';

export const Td = ({ children, ...props }: PropsWithChildren<BoxProps>) => {
  const { translate, ...restProps } = props as any;
  return (
    <ChakraTd
      borderBottom={'1px solid' as any}
      borderColor={'neutral.100' as any}
      color={'neutral.must' as any}
      pl={[0, null, 3] as any}
      pr={5 as any}
      py={[3, 2] as any}
      textStyle={'body2' as any}
      whiteSpace={'nowrap' as any}
      {...restProps}
    >
      {children}
    </ChakraTd>
  );
};

export const Th = ({ children, ...props }: PropsWithChildren<BoxProps>) => {
  const { translate, ...restProps } = props as any;
  return (
    <ChakraTh
      borderBottom={'1px solid' as any}
      borderColor={'neutral.100' as any}
      color={'neutral.800' as any}
      pl={[0, null, 3] as any}
      pr={5 as any}
      py={2 as any}
      textStyle={'body2-highlight' as any}
      textTransform={'none' as any}
      whiteSpace={'nowrap' as any}
      {...restProps}
    >
      {children}
    </ChakraTh>
  );
};
