import { useModalContext } from '@chakra-ui/modal';
import { Icon, IconButton, type IconButtonProps } from '@chakra-ui/react';
import { FiX } from 'react-icons/fi';

type ModalCloseButtonProps = Omit<IconButtonProps, 'aria-label'> & {
  iconColor?: string;
};

export const ModalCloseButton = ({
  iconColor,
  ...props
}: ModalCloseButtonProps): JSX.Element => {
  const { onClose } = useModalContext();
  return (
    <IconButton
      {...props}
      _active={{
        bg: 'neutral.100',
      }}
      _hover={{
        bg: 'neutral.50',
      }}
      aria-label="Close"
      bg="transparent"
      color="primary.800"
      h={[14, 12]}
      onClick={onClose}
      position="absolute"
      right={[0, 4]}
      top={[0, 4]}
      variant="ghost"
      w={[14, 12]}
    >
      <Icon as={FiX} boxSize="1.5rem" color={iconColor} />
    </IconButton>
  );
};
