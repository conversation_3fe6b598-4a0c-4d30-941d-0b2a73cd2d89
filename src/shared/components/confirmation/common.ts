// Placement type is no longer exported from @chakra-ui/react in v3
type Placement =
  | 'top'
  | 'bottom'
  | 'left'
  | 'right'
  | 'top-start'
  | 'top-end'
  | 'bottom-start'
  | 'bottom-end'
  | 'left-start'
  | 'left-end'
  | 'right-start'
  | 'right-end';

export type ConfirmationProps = {
  title: string;
  actionText: string;
  onAction: () => void | Promise<void>;
  popoverPlacement?: Placement;
  cyPrefix?: string;
};

export type ConfirmationDisplayElementProps = {
  trigger: JSX.Element;
} & ConfirmationProps;
