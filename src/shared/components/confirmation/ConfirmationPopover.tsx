import { Popover, PopoverContent, PopoverTrigger } from '@chakra-ui/popover';
import { Portal } from '@chakra-ui/react';

import type { ConfirmationDisplayElementProps } from './common';
import { ConfirmationContent } from './ConfirmationContent';

export const ConfirmationPopover = ({
  title,
  actionText,
  onAction,
  popoverPlacement,
  trigger,
  cyPrefix,
}: ConfirmationDisplayElementProps) => (
  <Popover gutter={0} isLazy placement={popoverPlacement}>
    {({ onClose }) => (
      <>
        <PopoverTrigger>{trigger}</PopoverTrigger>
        <Portal>
          <PopoverContent
            data-cy={[cyPrefix, 'confirmation-popover-content'].join('-')}
            minW="15rem"
            p={5}
            pt={4}
          >
            <ConfirmationContent
              actionText={actionText}
              cyPrefix={cyPrefix}
              onAction={onAction}
              onClose={onClose}
              title={title}
            />
          </PopoverContent>
        </Portal>
      </>
    )}
  </Popover>
);
