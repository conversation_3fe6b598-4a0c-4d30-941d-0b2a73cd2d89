import type { BoxProps } from '@chakra-ui/react';

// Placement type is no longer exported from @chakra-ui/react in v3
type Placement =
  | 'top'
  | 'bottom'
  | 'left'
  | 'right'
  | 'top-start'
  | 'top-end'
  | 'bottom-start'
  | 'bottom-end'
  | 'left-start'
  | 'left-end'
  | 'right-start'
  | 'right-end';
import {
  format,
  isSameDay,
  isSameMonth,
  isSameYear,
  type Locale,
} from 'date-fns';
import getTime from 'date-fns/getTime';
import en from 'date-fns/locale/en-GB';
import et from 'date-fns/locale/et';
import lt from 'date-fns/locale/lt';
import lv from 'date-fns/locale/lv';
import ru from 'date-fns/locale/ru';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { AvailableLanguage } from 'shared/types';

export type DateRangePickerProps = {
  value: DateRangeType;
  onChange: (range: DateRangeType | undefined) => void;
};

export type DateRangeElementProps = {
  placement?: Placement;
} & DateRangePickerProps &
  BoxProps;

export type DateRangeType = {
  from: Date | undefined;
  to?: Date | undefined;
};

const locales = {
  et,
  lt,
  en,
  ru,
  lv,
};

export function useDateFnsLocale(): Locale {
  const { i18n } = useTranslation();
  // @ts-expect-error - locales object may not have all language keys defined
  return locales[i18n.language as AvailableLanguage];
}

export function useFormattedDateRange({ to, from }: DateRangeType): {
  formattedDate: string;
  isSameDay: boolean;
} {
  const locale = useDateFnsLocale();

  return useMemo(() => {
    if (!from) {
      return { formattedDate: '', isSameDay: false };
    }

    if (!to || isSameDay(from, to)) {
      return {
        formattedDate: format(from, 'd MMM yyyy', { locale }),
        isSameDay: true,
      };
    }

    if (isSameMonth(from, to)) {
      return {
        formattedDate: `${format(from, 'd', { locale })}-${format(
          to,
          'd MMM yyyy',
          { locale },
        )}`,
        isSameDay: false,
      };
    }

    if (isSameYear(from, to)) {
      return {
        formattedDate: `${format(from, 'd MMM', { locale })} - ${format(
          to,
          'd MMM yyyy',
          { locale },
        )}`,
        isSameDay: false,
      };
    }

    return {
      formattedDate: `${format(from, 'd MMM yyyy', { locale })} - ${format(
        to,
        'd MMM yyyy',
        { locale },
      )}`,
      isSameDay: false,
    };
  }, [to, from, locale]);
}

export const useTimestamp = (date: Date | number): number =>
  // @ts-expect-error - getTime may not accept all date types but works in practice
  useMemo(() => Math.round(getTime(date) / 1000), [date]);

export function useFormattedDate(date: Date): string {
  return useMemo(() => {
    if (!date) {
      return '';
    }
    return format(date, 'yyyy-MM-dd');
  }, [date]);
}
