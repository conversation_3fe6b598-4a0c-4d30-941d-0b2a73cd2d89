import { FormErrorIcon, FormErrorMessage } from '@chakra-ui/form-control';
import {
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
} from '@chakra-ui/input';
import { Icon } from '@chakra-ui/react';
import { forwardRef } from 'react';
import { FiAlertCircle, FiCalendar } from 'react-icons/fi';

import { type BaseInputProps, InputWrapper } from '../controls/common';

type DateRangeInputProps = {
  isOpen?: boolean;
  value: string;
} & BaseInputProps;

export const DateRangeInput = forwardRef<HTMLInputElement, DateRangeInputProps>(
  ({ isOpen, value, label, error, isDisabled, ...chakraProps }, ref) => {
    const focusedStyles = {
      pl: 'calc(3rem - 1px)',
      pr: error ? 'calc(3rem - 1px)' : undefined,
    };

    return (
      <InputWrapper
        error={error}
        isDisabled={isDisabled}
        label={label}
        {...chakraProps}
      >
        <InputGroup>
          <InputLeftElement width={12}>
            <Icon as={FiCalendar} boxSize={6} color="primary.800" />
          </InputLeftElement>
          <Input
            _focus={focusedStyles}
            cursor="pointer"
            ref={ref}
            value={value}
            {...(isOpen ? focusedStyles : {})}
            _selection={{ color: 'inherit' }}
            readOnly
            pl={12}
            pr={error ? 12 : undefined}
          />

          {!!error && (
            <InputRightElement width={12}>
              <FormErrorMessage>
                <FormErrorIcon as={FiAlertCircle} boxSize={6} color="red.700" />
              </FormErrorMessage>
            </InputRightElement>
          )}
        </InputGroup>
      </InputWrapper>
    );
  },
);
DateRangeInput.displayName = 'DateRangeInput';
