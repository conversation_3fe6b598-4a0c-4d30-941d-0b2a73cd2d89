import 'react-day-picker/dist/style.css';

import { Box, useMediaQuery, useToken } from '@chakra-ui/react';
import type React from 'react';
import { DayPicker } from 'react-day-picker';

import { type DateRangePickerProps, useDateFnsLocale } from './common';

export const DateRangePickerDesktop: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
}) => {
  const locale = useDateFnsLocale();
  const breakpointValue = useToken('breakpoints', 'md');
  const [isLargerThanBreakpoint] = useMediaQuery(
    `(min-width: ${breakpointValue})`,
  );

  return (
    <Box>
      <DayPicker
        defaultMonth={value.from}
        locale={locale}
        mode="range"
        numberOfMonths={isLargerThanBreakpoint ? 2 : 1}
        onSelect={(range) => {
          if (range) {
            onChange(range);
          }
        }}
        onDayClick={(
          day,
          { selected, range_start, range_middle, range_end },
        ) => {
          if (selected && range_start && !range_middle && !range_end) {
            onChange({ from: day, to: day });
          }
        }}
        selected={value}
        toDate={new Date()}
      />
    </Box>
  );
};

export const DateRangePickerMobile: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
}) => {
  const locale = useDateFnsLocale();
  return (
    <Box>
      <DayPicker
        defaultMonth={value.from}
        locale={locale}
        mode="range"
        numberOfMonths={1}
        onSelect={(range) => {
          if (range) {
            onChange(range);
          }
        }}
        onDayClick={(
          day,
          { selected, range_start, range_middle, range_end },
        ) => {
          if (selected && range_start && !range_middle && !range_end) {
            onChange({ from: day, to: day });
          }
        }}
        selected={value}
        toDate={new Date()}
      />
    </Box>
  );
};
