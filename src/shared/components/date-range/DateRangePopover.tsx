import { Popover, PopoverContent, PopoverTrigger } from '@chakra-ui/popover';
import { Portal } from '@chakra-ui/react';
import type React from 'react';

import { type DateRangeElementProps, useFormattedDateRange } from './common';
import { DateRangeInput } from './DateRangeInput';
import { DateRangePickerDesktop } from './DateRangePicker';

export const DateRangePopover: React.FC<DateRangeElementProps> = ({
  placement,
  value,
  onChange,
  ...chakraProps
}) => {
  const { formattedDate } = useFormattedDateRange(value);
  return (
    <Popover gutter={0} isLazy placement={placement} returnFocusOnClose={false}>
      {({ isOpen }) => (
        <>
          <PopoverTrigger>
            <DateRangeInput
              isOpen={isOpen}
              value={formattedDate}
              {...chakraProps}
            />
          </PopoverTrigger>
          <Portal>
            <PopoverContent p={5} pt={4}>
              <DateRangePickerDesktop onChange={onChange} value={value} />
            </PopoverContent>
          </Portal>
        </>
      )}
    </Popover>
  );
};
