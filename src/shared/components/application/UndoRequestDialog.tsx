import { Button, Dialog } from '@chakra-ui/react';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  LocizeApplicationsKeys,
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import { ButtonWithLoader } from '../ButtonWithLoader';

type Props = {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  isLoading: boolean;
};

export const UndoApplicationRequestDialog = ({
  open,
  onClose,
  isLoading,
  onSubmit,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);

  const cancelRef = useRef<HTMLButtonElement>(null);

  return (
    <Dialog.Root
      placement="center"
      open={open}
      onOpenChange={(details: any) => !details.open && onClose()}
    >
      <Dialog.Backdrop />
      <Dialog.Positioner>
        <Dialog.Content width="400px">
          <Dialog.Header>
            {t(LocizeApplicationsKeys.UNDO_REQUEST_MODAL_TITLE)}
          </Dialog.Header>
          <Dialog.CloseTrigger />
          <Dialog.Footer>
            <Button
              colorScheme={ColorSchemes.SECONDARY}
              disabled={isLoading}
              onClick={onClose}
              ref={cancelRef}
            >
              {tc(LocizeCommonKeys.FORMS_CANCEL)}
            </Button>
            <ButtonWithLoader loading={isLoading} ml={3} onClick={onSubmit}>
              {tc(LocizeCommonKeys.FORMS_SUBMIT)}
            </ButtonWithLoader>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog.Positioner>
    </Dialog.Root>
  );
};
