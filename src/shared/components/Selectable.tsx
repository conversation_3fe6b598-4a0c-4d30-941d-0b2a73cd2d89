import { Button, type ButtonProps } from '@chakra-ui/react';
import { forwardRef } from 'react';

import type { ColorSchemes } from '../chakra-theme/foundations/colors';

type SelectableProps = {
  isSelected: boolean;
  isDisabled?: boolean;
  onClick: () => void;
  children?: React.ReactNode;
  colorScheme?: ColorSchemes;
} & Omit<ButtonProps, 'onClick'>;

const getColors = (c = 'primary'): { bg: string; borderColor: string } => {
  switch (c) {
    case 'neutral':
      return {
        bg: `${c}.100`,
        borderColor: `${c}.800`,
      };
    default:
      return {
        bg: `${c}.100`,
        borderColor: `${c}.600`,
      };
  }
};

export const Selectable = forwardRef<HTMLButtonElement, SelectableProps>(
  (
    { isSelected, isDisabled, onClick, children, colorScheme, ...chakraProps },
    ref,
  ) => {
    const colors = getColors(colorScheme);
    return (
      <Button
        _active={{ bg: 'neutral.50' }}
        _focus={{ bg: 'neutral.50' }}
        _hover={{ bg: 'neutral.50' }}
        _selected={{
          ...colors,
          borderWidth: '2px',
          px: 'calc(1rem - 1px)',
          py: 'calc(0.75rem - 1px)',
        }}
        aria-selected={isSelected}
        bg="white"
        border="1px solid"
        borderColor="neutral.150"
        borderRadius="0.25rem"
        color="neutral.must"
        height="auto"
        disabled={isDisabled}
        onClick={onClick}
        px={4}
        py={3}
        ref={ref}
        textAlign="initial"
        transition="all 0.25s, border-width 0s, border-color 0s, padding 0s"
        width="full"
        {...chakraProps}
      >
        {children}
      </Button>
    );
  },
);
Selectable.displayName = 'Selectable';
