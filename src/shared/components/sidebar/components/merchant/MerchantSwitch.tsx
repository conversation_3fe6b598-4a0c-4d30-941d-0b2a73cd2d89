import type { BoxProps } from '@chakra-ui/react';
import { useContext, useMemo } from 'react';
import type { MerchantSwitchElementProps } from 'shared/components/sidebar/components/merchant/common';
import { MerchantDropdown } from 'shared/components/sidebar/components/merchant/MerchantDropdown';
import { MerchantList } from 'shared/components/sidebar/components/merchant/MerchantList';
import { MerchantModal } from 'shared/components/sidebar/components/merchant/MerchantModal';
import { MerchantSwitchTrigger } from 'shared/components/sidebar/components/merchant/MerchantSwitchTrigger';
import { useLayoutAwareElement } from 'shared/hooks/layout';
import { useAssignedMerchants } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';

export const MerchantSwitch = (chakraProps: BoxProps) => {
  const { ...buttonProps } = chakraProps as any;
  const { merchantId, setMerchantId } = useContext(GlobalStateContext);
  const merchants = useAssignedMerchants();

  const selectedMerchant = useMemo(
    () => merchants.find(({ id }) => id === merchantId) || null,
    [merchantId, merchants],
  );

  const SwitchElement = useLayoutAwareElement<MerchantSwitchElementProps>(
    MerchantModal,
    MerchantDropdown,
  );

  return (
    <SwitchElement
      list={({ onClose, focusRef, size }) => (
        <MerchantList
          focusRef={focusRef}
          merchants={merchants}
          onSelect={(merch) => {
            setMerchantId(merch.id);
            onClose();
          }}
          selectedMerchant={selectedMerchant}
          size={size}
        />
      )}
      trigger={
        <MerchantSwitchTrigger
          data-cy="merchant-switch-trigger"
          selectedMerchant={selectedMerchant}
          {...buttonProps}
          disabled={merchants.length === 0}
        />
      }
    />
  );
};
