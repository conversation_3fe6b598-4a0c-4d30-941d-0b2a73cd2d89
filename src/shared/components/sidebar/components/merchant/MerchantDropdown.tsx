import { Popover, PopoverContent, PopoverTrigger } from '@chakra-ui/popover';
import { Portal } from '@chakra-ui/react';
import { useRef } from 'react';
import type { MerchantSwitchElementProps } from 'shared/components/sidebar/components/merchant/common';

export const MerchantDropdown = ({
  trigger,
  list,
}: MerchantSwitchElementProps) => {
  const focusRef = useRef<HTMLButtonElement>(null);

  return (
    <Popover
      gutter={0}
      id="merchant-switch"
      initialFocusRef={focusRef}
      isLazy
      placement="right-start"
    >
      {({ onClose }) => (
        <>
          <PopoverTrigger>{trigger}</PopoverTrigger>
          <Portal>
            <PopoverContent
              data-cy="merchant-switch-popover-content"
              minW="15rem"
            >
              {list({ onClose, focusRef, size: 'small' })}
            </PopoverContent>
          </Portal>
        </>
      )}
    </Popover>
  );
};
