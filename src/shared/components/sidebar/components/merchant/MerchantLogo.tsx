import { Avatar } from '@chakra-ui/avatar';
import { memo } from 'react';
import type { MerchantListItem } from 'shared/types';

type MerchantLogoProps = {
  merchant: MerchantListItem;
  isLight?: boolean;
};

export const MerchantLogo = memo(
  ({ merchant, isLight }: MerchantLogoProps): JSX.Element => {
    return (
      <Avatar
        bg={isLight ? 'white' : 'neutral.50'}
        borderColor="neutral.100"
        borderWidth={isLight ? undefined : '1px'}
        boxSize={6}
        color="neutral.800"
        css={{
          '& > img': { objectFit: 'contain', backgroundColor: 'white' },
          '& > .chakra-avatar__initials': {
            fontSize: '12px',
          },
        }}
        data-cy="merchant-switch-merchant-logo"
        flexShrink={0}
        getInitials={(name) => name[0]}
        name={merchant.name}
        showBorder={!isLight}
        src={merchant.logo_path ?? undefined}
      />
    );
  },
);
MerchantLogo.displayName = 'MerchantLogo';
