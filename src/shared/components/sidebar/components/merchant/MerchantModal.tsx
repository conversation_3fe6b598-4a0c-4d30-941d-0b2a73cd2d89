import { Modal, ModalBody, ModalContent, ModalHeader } from '@chakra-ui/modal';
import { useDisclosure } from '@chakra-ui/react';
import { cloneElement, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalCloseButton } from 'shared/components/ModalCloseButton';
import type { MerchantSwitchElementProps } from 'shared/components/sidebar/components/merchant/common';

export const MerchantModal = ({
  trigger,
  list,
}: MerchantSwitchElementProps) => {
  const { open, onClose, onOpen } = useDisclosure();
  const { t } = useTranslation();
  const focusRef = useRef<HTMLButtonElement>(null);

  return (
    <>
      {cloneElement(trigger, { ...trigger.props, onClick: onOpen })}
      <Modal initialFocusRef={focusRef} isOpen={open} onClose={onClose}>
        <ModalContent data-cy="merchant-switch-modal-content">
          <ModalHeader>{t('merchant-switch.modal-header')}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>{list({ onClose, focusRef })}</ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};
