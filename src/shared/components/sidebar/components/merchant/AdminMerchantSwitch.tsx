import {
  Modal,
  ModalBody,
  ModalContent,
  ModalHeader,
  ModalOverlay,
} from '@chakra-ui/modal';
import {
  type BoxProps,
  Icon,
  useBreakpointValue,
  useDisclosure,
} from '@chakra-ui/react';
import { useContext, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiSearch } from 'react-icons/fi';
import { TextInput } from 'shared/components/controls';
import { Loader } from 'shared/components/Loader';
import { ModalCloseButton } from 'shared/components/ModalCloseButton';
import { MerchantList } from 'shared/components/sidebar/components/merchant/MerchantList';
import { MerchantSwitchTrigger } from 'shared/components/sidebar/components/merchant/MerchantSwitchTrigger';
import { useAdminMerchants, useMerchantDetails } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';
import { useDebouncedValue } from 'shared/hooks/utils';

export const AdminMerchantSwitch = (chakraProps: BoxProps) => {
  const [search, setSearch] = useState('');
  const debouncedSearch = useDebouncedValue(search, 500);

  const { setMerchantId } = useContext(GlobalStateContext);
  const { open, onClose, onOpen } = useDisclosure();
  const { t } = useTranslation();
  const { data } = useMerchantDetails();
  const selectedMerchant = data?.merchant || null;
  const { isLoading, merchants } = useAdminMerchants(debouncedSearch, {
    skip: !open,
  });

  const focusRef = useRef<HTMLButtonElement>(null);
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <>
      <MerchantSwitchTrigger
        data-cy="merchant-switch-trigger"
        onClick={onOpen}
        selectedMerchant={selectedMerchant}
        {...chakraProps}
      />
      <Modal
        initialFocusRef={focusRef}
        isOpen={open}
        onClose={onClose}
        scrollBehavior="inside"
      >
        {!isMobile && <ModalOverlay />}
        <ModalContent
          data-cy="admin-merchant-switch-modal-content"
          maxW="29rem"
        >
          <ModalHeader>{t('merchant-switch.modal-header')}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <TextInput
              data-cy="admin-merchant-switch-search"
              name="admin-merchant-searcb"
              onChange={(e) => {
                setSearch(e.target.value);
              }}
              placeholder={t('merchant-switch.search-placeholder')}
              startElement={
                <Icon as={FiSearch} boxSize={6} color="neutral.800" />
              }
              value={search}
            />
            {isLoading ? (
              <Loader />
            ) : (
              <MerchantList
                focusRef={focusRef}
                merchants={merchants || []}
                onSelect={(merch) => {
                  setMerchantId(merch.id);
                  onClose();
                }}
                selectedMerchant={selectedMerchant}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};
