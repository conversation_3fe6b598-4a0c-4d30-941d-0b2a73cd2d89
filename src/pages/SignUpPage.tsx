import { useApolloClient } from '@apollo/client';
import { Box, Button, Text } from '@chakra-ui/react';
import { PasswordSignUp } from 'modules/auth/sign-up';
import { useCallback, useContext, useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { UserDataDocument, type UserDataQuery } from 'shared/api';
import { LoggedOutLayout } from 'shared/components';
import { DesktopHeader, MobileHeader } from 'shared/components/login/Headers';
import {
  LoginRoute,
  SignUpRoute,
  TerminalRoute,
} from 'shared/constants/routes';
import { useHandleGenericError, useShowError } from 'shared/hooks/alerts';
import { usePageTitle } from 'shared/hooks/app';
import { useInviteHash, useMerchantInvite } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';

const SignUpPage = () => {
  const { isVisitedInvitePage, setIsVisitedInvitePage } =
    useContext(GlobalStateContext);
  const [isAfterSignUpSetupInProgress, setIsAfterSignUpSetupInProgress] =
    useState(false);
  const apollo = useApolloClient();
  const navigate = useNavigate();
  const handleGenericError = useHandleGenericError();

  const { t } = useTranslation('signup');
  const showError = useShowError();

  const inviteHash = useInviteHash();
  const { data, loading, error } = useMerchantInvite(inviteHash);

  if (error) {
    showError(error.message);
    navigate('/', {
      replace: true,
    });
  }

  useEffect(() => {
    if (!isVisitedInvitePage) {
      setIsVisitedInvitePage(String(inviteHash));
    }
    navigate(SignUpRoute.format({ inviteHash }), {
      replace: true,
    });
  }, [navigate, inviteHash, setIsVisitedInvitePage, isVisitedInvitePage]);

  const onSignUpSuccess = useCallback(async () => {
    setIsAfterSignUpSetupInProgress(true);
    await apollo.resetStore();
    // prefetch current user
    try {
      await apollo.query<UserDataQuery>({ query: UserDataDocument });
      setIsAfterSignUpSetupInProgress(false);
      navigate(TerminalRoute.template);
    } catch (err) {
      handleGenericError(err);
      setIsAfterSignUpSetupInProgress(false);
    }
  }, [navigate, handleGenericError, apollo]);

  const email = data?.merchant_invite?.email ?? '';

  usePageTitle(t('page-title'));

  return (
    <LoggedOutLayout
      desktopHeader={DesktopHeader}
      isLoading={loading || isAfterSignUpSetupInProgress}
      mobileHeader={MobileHeader}
    >
      <Box pb={12} pt={[6, '50px']}>
        <Text mb="2" textStyle="h2">
          {t('page-title')}
        </Text>

        <Text mb="6" textStyle="body2">
          <Trans
            components={{ strong: <strong /> }}
            i18nKey="subtitle"
            ns="signup"
            values={{ email: data?.merchant_invite?.email }}
          />
        </Text>

        <PasswordSignUp email={email} onSuccess={onSignUpSuccess} />
        <Link to={LoginRoute.format()}>
          <Button
            bg="transparent"
            color="primary.800"
            data-cy="account-already-created"
            mt={4}
            width="full"
          >
            {t('already-have-account')}
          </Button>
        </Link>
        <Text color="neutral.800" mb="3" mt="6" textStyle="body2">
          {t('methods-footer-message')}
        </Text>
      </Box>
    </LoggedOutLayout>
  );
};

export default SignUpPage;
