import { Box } from '@chakra-ui/react';
import { ConnectId, Password, PersonalInformation } from 'modules/account';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Header } from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { usePageTitle } from 'shared/hooks/app';
import { useCurrentUser } from 'shared/hooks/user';

const AccountPage = () => {
  const { t } = useTranslation(LocizeNamespaces.COMMON);
  const { user } = useCurrentUser();
  const personalInfoDefaultValue = useMemo(
    () => ({
      firstName: user?.profile?.first_name || '',
      lastName: user?.profile?.last_name || '',
    }),
    [user?.profile?.first_name, user?.profile?.last_name],
  );
  const hasPin = !!user?.pin;

  usePageTitle(t(LocizeCommonKeys.SIDEBAR_ACCOUNT_OPTION));

  return (
    <>
      <Header title={t(LocizeCommonKeys.SIDEBAR_ACCOUNT_OPTION)} />
      <Box
        mx="auto"
        pb={['48px', null, '100px']}
        pt={{
          base: 8,
          md: '20px',
        }}
        px="20px"
        w={['100%', '400px']}
      >
        <PersonalInformation
          defaultValues={personalInfoDefaultValue}
          isDisabled={hasPin}
          mb={[5, 9]}
        />
        {!hasPin && <ConnectId />}
        <Password hasPassword={!!user?.is_password_set} />
      </Box>
    </>
  );
};

export default AccountPage;
