import { Box } from '@chakra-ui/react';
import { useStore } from 'effector-react';
import {
  EmployeesTable,
  InviteEmployeeModal,
  TabDesktopTitle,
} from 'modules/settings';
import {
  $isEmployeeInviteModalOpen,
  toggleEmployeeInviteModal,
} from 'modules/settings/employees/invite-employee-modal';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader } from 'shared/components';
import { usePageTitle } from 'shared/hooks/app';
import { useMerchantUsers } from 'shared/hooks/merchant';
import { SettingsRouteTabs } from 'shared/types';

const EmployeesSettingsPage = () => {
  const { t } = useTranslation(['settings', 'common']);
  const { isLoading, users } = useMerchantUsers();
  const employees = users || [];

  const isOpenInviteEmployeeModal = useStore($isEmployeeInviteModalOpen);
  const handleInviteEmployeeModalClose = useCallback(() => {
    toggleEmployeeInviteModal(true);
  }, []);

  usePageTitle(t('common:sidebar.settings'));

  if (isLoading) {
    return <Loader />;
  }

  return (
    <Box
      mx={-5}
      pb={['48px', null, '100px']}
      pt={[0, null, 8]}
      px={[0, null, 10]}
    >
      <TabDesktopTitle
        actionText={t('employees.invite-modal-btn')}
        count={employees.length}
        mb={6}
        onAction={handleInviteEmployeeModalClose}
        tab={SettingsRouteTabs.Employees}
      />
      <EmployeesTable />
      <InviteEmployeeModal open={isOpenInviteEmployeeModal} />
    </Box>
  );
};

export default EmployeesSettingsPage;
