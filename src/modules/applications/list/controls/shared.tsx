import { HStack, Icon, Text } from '@chakra-ui/react';
import { Switch } from '@chakra-ui/react';
import { applicationsTableState } from 'modules/applications/shared';
import { type ChangeEvent, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FiSearch } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { TextInput } from 'shared/components';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { useDebounce } from 'shared/hooks/utils';

const DEBOUNCE_TIME = 500;

export const SearchInputForApplicationTable = () => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);

  const handleSearchChange = useDebounce(
    useCallback((...args: unknown[]) => {
      const e = args[0] as ChangeEvent<HTMLInputElement>;
      applicationsTableState.search(e.target.value);
      applicationsTableState.page(1);
    }, []),
    DEBOUNCE_TIME,
  );

  return (
    <TextInput
      flexGrow={1}
      minW={{
        md: '12.5rem',
      }}
      mx={{
        base: 1,
        md: 0,
      }}
      onChange={handleSearchChange}
      placeholder={t('filters.search')}
      startElement={<Icon as={FiSearch} boxSize={6} />}
    />
  );
};

type SwitchFilterProps = {
  cyPrefix?: string;
  isChecked: boolean;
  label: string;
  onToggle: () => void;
};

export const SwitchFilter = ({
  isChecked,
  cyPrefix,
  label,
  onToggle,
}: SwitchFilterProps) => (
  <HStack
    alignItems="center"
    data-cy={`${cyPrefix}-applications-switch`}
    flexShrink={0}
    gap={2}
  >
    <Switch.Root
      colorPalette={ColorSchemes.PRIMARY}
      checked={isChecked}
      onCheckedChange={onToggle}
    >
      <Switch.HiddenInput />
      <Switch.Control>
        <Switch.Thumb />
      </Switch.Control>
    </Switch.Root>
    <Text textStyle="body1">{label}</Text>
  </HStack>
);
