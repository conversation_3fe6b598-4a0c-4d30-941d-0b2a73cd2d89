import { useReactiveVar } from '@apollo/client';
import { Checkbox } from '@chakra-ui/checkbox';
import {
  Box,
  Button,
  Center,
  Dialog,
  HStack,
  Icon,
  IconButton,
  Text,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiFilter } from 'react-icons/fi';
import { AdminApplicationStatuses, ApplicationScheduleType } from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { STATUSES_WITH_BADGES } from 'shared/types/application';

import {
  applicationsTableState,
  defaultApplicationTableState,
  PaymentMethodBadge,
  StatusBadge,
  useFilterPaymentMethods,
} from '../shared';

export const MobileFilters = () => {
  const statuses = useReactiveVar(applicationsTableState.statuses);
  const paymentMethods = useReactiveVar(applicationsTableState.paymentMethods);
  const isIssuedByMe = useReactiveVar(applicationsTableState.issuedByMe);
  const { open, onOpen, onClose } = useDisclosure();

  const onReset = () => {
    applicationsTableState.page(defaultApplicationTableState.page);
    applicationsTableState.statuses(defaultApplicationTableState.statuses);
    applicationsTableState.issuedByMe(defaultApplicationTableState.issuedByMe);
    applicationsTableState.search(defaultApplicationTableState.search);
    applicationsTableState.paymentMethods(
      defaultApplicationTableState.paymentMethods,
    );
    onClose();
  };

  const onApply = (
    statuses: Array<AdminApplicationStatuses>,
    paymentMethods: Array<ApplicationScheduleType>,
    issuedByMe: boolean,
  ) => {
    applicationsTableState.page(1);
    applicationsTableState.statuses(statuses);
    applicationsTableState.paymentMethods(paymentMethods);
    applicationsTableState.issuedByMe(issuedByMe);
    onClose();
  };

  const adjustedPaymentMethods = paymentMethods.filter(
    (method) =>
      ![
        ApplicationScheduleType.FAST_LOAN,
        ApplicationScheduleType.RENOVATION_LOAN,
        ApplicationScheduleType.VEHICLE_LOAN,
      ].includes(method),
  ); // we count them here as SMALL_LOAN so we need to filter them out

  return (
    <>
      <Box position="relative">
        <IconButton
          aria-label="reset"
          data-cy="applications-mobile-filter-toggle"
          onClick={onOpen}
          variant="ghost"
        >
          <Icon as={FiFilter} boxSize={6} color="primary.800" />
        </IconButton>
        <Center
          bg="neutral.must"
          borderRadius="full"
          boxSize={4}
          color="white"
          data-cy="applications-selected-filters-count"
          position="absolute"
          right={2}
          textStyle="caption-highlight"
          top={2}
        >
          {statuses.length +
            adjustedPaymentMethods.length +
            (isIssuedByMe ? 1 : 0)}
        </Center>
      </Box>
      <ApplicationsFilterModal
        open={open}
        onApply={onApply}
        onClose={onClose}
        onReset={onReset}
      />
    </>
  );
};

type ApplicationsFilterModalProps = {
  open: boolean;
  onReset: () => void;
  onClose: () => void;
  onApply: (
    statuses: Array<AdminApplicationStatuses>,
    paymentMethods: Array<ApplicationScheduleType>,
    issuedByMe: boolean,
  ) => void;
};

export const ApplicationsFilterModal = ({
  open,
  onClose,
  ...rest
}: ApplicationsFilterModalProps) => (
  <Dialog.Root
    open={open}
    onOpenChange={(details: any) => !details.open && onClose()}
  >
    <ApplicationsFilterModalContent {...rest} />
  </Dialog.Root>
);

const ApplicationsFilterModalContent = ({
  onReset,
  onApply,
}: Pick<ApplicationsFilterModalProps, 'onReset' | 'onApply'>) => {
  const { t } = useTranslation('applications');

  const selectPaymentMethods = useFilterPaymentMethods();

  const tableStateStatuses = useReactiveVar(applicationsTableState.statuses);
  const tableStatePaymentMethods = useReactiveVar(
    applicationsTableState.paymentMethods,
  );
  const isTableStateIssuedByMe = useReactiveVar(
    applicationsTableState.issuedByMe,
  );

  const [statuses, setStatuses] = useState(tableStateStatuses);
  const [paymentMethods, setPaymentMethods] = useState(
    tableStatePaymentMethods,
  );
  const [isIssuedByMe, setIssuedByMe] = useState(isTableStateIssuedByMe);

  const selectPaymentMethodValue = selectPaymentMethods.filter((item) =>
    paymentMethods.some((value) => item.value.includes(value)),
  );

  const isStatusSelected = (status: AdminApplicationStatuses): boolean =>
    statuses.includes(status);

  const onToggleStatus = (toggledStatus: AdminApplicationStatuses): void => {
    setStatuses((prev) =>
      isStatusSelected(toggledStatus)
        ? prev.filter((status) => status !== toggledStatus)
        : [...prev, toggledStatus],
    );
  };

  const isPaymentMethodSelected = (
    paymentMethod: ApplicationScheduleType[],
  ): boolean =>
    selectPaymentMethodValue.some(({ value }) =>
      value.some((method) => paymentMethod.includes(method)),
    );
  const onTogglePaymentMethod = (
    paymentMethod: ApplicationScheduleType[],
  ): void => {
    setPaymentMethods((prev) =>
      isPaymentMethodSelected(paymentMethod)
        ? prev.filter((prevMethod) => !paymentMethod.includes(prevMethod))
        : [...prev, ...paymentMethod],
    );
  };

  return (
    <Dialog.Positioner>
      <Dialog.Content data-cy="applications-filter-modal">
        <Dialog.Header>
          <Dialog.Title>{t('filters.title')}</Dialog.Title>
          <Dialog.CloseTrigger data-cy="applications-filter-modal-close" />
        </Dialog.Header>
        <Dialog.Body p={0}>
          <VStack
            alignItems="stretch"
            borderBottom="1px solid"
            borderColor="neutral.150"
            p={5}
            gap={6}
          >
            <Checkbox
              data-cy="applications-mobile-filter-issued-by-me"
              isChecked={isIssuedByMe}
              onChange={() => {
                setIssuedByMe((prev) => !prev);
              }}
            >
              {t('filters.issued-by-me')}
            </Checkbox>
            <Checkbox
              data-cy="applications-mobile-filter-test"
              isChecked={isStatusSelected(AdminApplicationStatuses.TEST_MODE)}
              onChange={() => {
                onToggleStatus(AdminApplicationStatuses.TEST_MODE);
              }}
            >
              {t('filters.test-applications')}
            </Checkbox>
          </VStack>
          <VStack
            alignItems="stretch"
            borderBottom="1px solid"
            borderColor="neutral.150"
            p={5}
            gap={6}
          >
            <Text textStyle="h4">{t('filters.statuses-title')}</Text>
            {STATUSES_WITH_BADGES.map((status) => (
              <Checkbox
                data-cy={`applications-mobile-filter-${status}`}
                isChecked={isStatusSelected(status)}
                key={status}
                onChange={() => {
                  onToggleStatus(status);
                }}
              >
                <StatusBadge ml={2} status={status} />
              </Checkbox>
            ))}
          </VStack>
          <VStack alignItems="stretch" p={5} gap={6}>
            <Text textStyle="h4">{t('filters.methods-title')}</Text>
            {selectPaymentMethods.map(({ value, label }) => (
              <Checkbox
                data-cy={`applications-mobile-filter-${status}`}
                isChecked={isPaymentMethodSelected(value)}
                key={`${value}-${label}`}
                onChange={() => {
                  onTogglePaymentMethod(value);
                }}
              >
                <PaymentMethodBadge ml={2} paymentMethod={value[0]} />
              </Checkbox>
            ))}
          </VStack>
        </Dialog.Body>

        <Dialog.Footer>
          <HStack gap={2} w="100%">
            <Button
              colorScheme={ColorSchemes.SECONDARY}
              data-cy="applications-mobile-filter-reset"
              onClick={onReset}
              width="full"
            >
              {t('filters.reset')}
            </Button>
            <Button
              colorScheme={ColorSchemes.PRIMARY}
              data-cy="applications-mobile-filter-submit"
              onClick={() => {
                onApply(statuses, paymentMethods, isIssuedByMe);
              }}
              width="full"
            >
              {t('filters.apply')}
            </Button>
          </HStack>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Positioner>
  );
};
