import { makeVar } from '@apollo/client';
import { Badge, type BoxProps, Text } from '@chakra-ui/react';
import { Tooltip } from '@chakra-ui/tooltip';
import { format } from 'date-fns';
import { type FC, memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AdminApplicationStatuses,
  ApplicationScheduleType,
  ApplicationStatus,
} from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import {
  type ApplicationDirectPaymentRefundItem,
  type ApplicationDirectPaymentRefunds,
  type ApplicationType,
  RefundStatuses,
  type StatusWithBadge,
} from 'shared/types/application';

export type ApplicationsTableState = {
  page: number;
  search: string;
  statuses: Array<AdminApplicationStatuses>;
  paymentMethods: Array<ApplicationScheduleType>;
  issuedByMe: boolean;
};

export const defaultApplicationTableState: ApplicationsTableState = {
  search: '',
  statuses: [
    AdminApplicationStatuses.SIGNED,
    AdminApplicationStatuses.REJECTED,
    AdminApplicationStatuses.NO_ACTION,
    AdminApplicationStatuses.CANCELLED,
    AdminApplicationStatuses.NO_USER,
  ],
  paymentMethods: [
    ApplicationScheduleType.REGULAR,
    ApplicationScheduleType.ESTO_X,
    ApplicationScheduleType.PAY_LATER,
    ApplicationScheduleType.ESTO_PAY,
    ApplicationScheduleType.SMALL_LOAN,
    ApplicationScheduleType.FAST_LOAN,
    ApplicationScheduleType.RENOVATION_LOAN,
    ApplicationScheduleType.VEHICLE_LOAN,
  ],
  page: 1,
  issuedByMe: false,
};

export const applicationsTableState = {
  page: makeVar(defaultApplicationTableState.page),
  search: makeVar(defaultApplicationTableState.search),
  statuses: makeVar(defaultApplicationTableState.statuses),
  paymentMethods: makeVar(defaultApplicationTableState.paymentMethods),
  issuedByMe: makeVar(defaultApplicationTableState.issuedByMe),
};

export const useApplicationStatusesFilterOptions = (): Array<{
  label: string;
  value: StatusWithBadge;
}> => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);

  return useMemo(
    () => [
      {
        label: t(
          LocizeApplicationsKeys.ApplicationsTable
            .APPLICATIONS_TABLE_STATUS_FILTER_NO_ACTION_PENDING_OPTION,
        ),
        value: AdminApplicationStatuses.NO_ACTION,
      },
      {
        label: t(
          LocizeApplicationsKeys.ApplicationsTable
            .APPLICATIONS_TABLE_STATUS_FILTER_NO_USER_OPTION,
        ),
        value: AdminApplicationStatuses.NO_USER,
      },
      {
        label: t(
          LocizeApplicationsKeys.ApplicationsTable
            .APPLICATIONS_TABLE_STATUS_FILTER_COMPLETED_OPTION,
        ),
        value: AdminApplicationStatuses.SIGNED,
      },
      {
        label: t(
          LocizeApplicationsKeys.ApplicationsTable
            .APPLICATIONS_TABLE_STATUS_FILTER_CANCELLED_OPTION,
        ),
        value: AdminApplicationStatuses.CANCELLED,
      },
      {
        label: t(
          LocizeApplicationsKeys.ApplicationsTable
            .APPLICATIONS_TABLE_STATUS_FILTER_REJECTED_OPTION,
        ),
        value: AdminApplicationStatuses.REJECTED,
      },
    ],
    [t],
  );
};

export const useFilterPaymentMethods = (): Array<{
  label: string;
  value: Array<ApplicationScheduleType>;
}> => {
  const { t } = useTranslation('applications');
  return useMemo(
    () => [
      {
        label: t('schedule-types.REGULAR'),
        value: [ApplicationScheduleType.REGULAR],
      },
      {
        label: t('schedule-types.ESTO_X'),
        value: [ApplicationScheduleType.ESTO_X],
      },
      {
        label: t('schedule-types.PAY_LATER'),
        value: [ApplicationScheduleType.PAY_LATER],
      },
      {
        label: t('schedule-types.ESTO_PAY'),
        value: [ApplicationScheduleType.ESTO_PAY],
      },
      {
        label: t('schedule-types.SMALL_LOAN'),
        value: [
          ApplicationScheduleType.SMALL_LOAN,
          ApplicationScheduleType.FAST_LOAN,
          ApplicationScheduleType.RENOVATION_LOAN,
          ApplicationScheduleType.VEHICLE_LOAN,
        ],
      },
    ],
    [t],
  );
};

export function useMappedStatusData(status: StatusWithBadge): {
  colorScheme: ColorSchemes;
  label: string;
  tooltip: string;
} {
  const { t } = useTranslation('applications');

  return useMemo(() => {
    switch (status) {
      case AdminApplicationStatuses.NO_USER:
        return {
          colorScheme: ColorSchemes.NEUTRAL,
          label: t('statuses.no-user'),
          tooltip: t('status-descriptions.no-user'),
        };

      case AdminApplicationStatuses.CANCELLED:
        return {
          colorScheme: ColorSchemes.ORANGE,
          label: t('statuses.cancelled'),
          tooltip: t('status-descriptions.cancelled'),
        };
      case AdminApplicationStatuses.REJECTED:
        return {
          colorScheme: ColorSchemes.RED,
          label: t('statuses.rejected'),
          tooltip: t('status-descriptions.rejected'),
        };
      case AdminApplicationStatuses.SIGNED:
        return {
          colorScheme: ColorSchemes.GREEN,
          label: t('statuses.completed'),
          tooltip: t('status-descriptions.completed'),
        };
      default:
        return {
          colorScheme: ColorSchemes.NEUTRAL,
          label: t('statuses.no-action-pending'),
          tooltip: t('status-descriptions.no-action'),
        };
    }
  }, [status, t]);
}

type SelectStatusItemProps = {
  status: StatusWithBadge;
  withTooltip?: boolean;
} & BoxProps;

type SelectPaymentMethodProps = {
  paymentMethod: ApplicationScheduleType;
  withTooltip?: boolean;
} & BoxProps;

type BadgeElementProps = {
  tooltip?: string;
  colorScheme: ColorSchemes;
  withTooltip?: boolean;
  label: string;
} & BoxProps;

const BadgeElement: FC<BadgeElementProps> = ({
  tooltip,
  withTooltip,
  colorScheme,
  label,
  ...chakraProps
}) => {
  return withTooltip ? (
    <Tooltip arrowSize={8} hasArrow label={tooltip} placement="top">
      <Badge colorScheme={colorScheme} {...chakraProps}>
        {label}
      </Badge>
    </Tooltip>
  ) : (
    <Badge colorScheme={colorScheme} {...chakraProps}>
      {label}
    </Badge>
  );
};

export const PaymentMethodBadge = memo(
  ({ paymentMethod, ...chakraProps }: SelectPaymentMethodProps) => {
    const { t } = useTranslation('applications');

    const { colorScheme, label } = useMemo(() => {
      switch (paymentMethod) {
        case ApplicationScheduleType.ESTO_PAY: {
          return {
            colorScheme: ColorSchemes.BLUE,
            label: t('schedule-types.ESTO_PAY'),
          };
        }
        case ApplicationScheduleType.ESTO_X: {
          return {
            colorScheme: ColorSchemes.ORANGE,
            label: t('schedule-types.ESTO_X'),
          };
        }
        case ApplicationScheduleType.PAY_LATER: {
          return {
            colorScheme: ColorSchemes.RED,
            label: t('schedule-types.PAY_LATER'),
          };
        }
        case ApplicationScheduleType.FAST_LOAN:
        case ApplicationScheduleType.RENOVATION_LOAN:
        case ApplicationScheduleType.VEHICLE_LOAN:
        case ApplicationScheduleType.SMALL_LOAN: {
          return {
            colorScheme: ColorSchemes.PRIMARY,
            label: t('schedule-types.SMALL_LOAN'),
          };
        }
        default: {
          return {
            colorScheme: ColorSchemes.GREEN,
            label: t('schedule-types.REGULAR'),
          };
        }
      }
    }, [paymentMethod, t]);

    return (
      <BadgeElement
        colorScheme={colorScheme as any}
        label={label}
        {...chakraProps}
      />
    );
  },
);
PaymentMethodBadge.displayName = 'PaymentMethodBadge';

export const StatusBadge: FC<SelectStatusItemProps> = memo(
  ({ status, withTooltip, ...chakraProps }) => {
    const { colorScheme, label, tooltip } = useMappedStatusData(status);

    return (
      <BadgeElement
        colorScheme={colorScheme as any}
        label={label}
        tooltip={tooltip}
        withTooltip={withTooltip}
        {...chakraProps}
      />
    );
  },
);
StatusBadge.displayName = 'ApplicationStatusBadge';

type GetStatusWithBadgeParams = Partial<
  Pick<ApplicationType, 'status' | 'schedule_type' | 'user_id'>
>;

export function getStatusWithBadge({
  status,
  user_id,
  schedule_type,
}: GetStatusWithBadgeParams): StatusWithBadge {
  if (!user_id && schedule_type !== ApplicationScheduleType.ESTO_PAY) {
    return AdminApplicationStatuses.NO_USER;
  }

  switch (status) {
    case ApplicationStatus.CANCELLED:
    case ApplicationStatus.DELETED:
      return AdminApplicationStatuses.CANCELLED;
    case ApplicationStatus.REJECTED:
      return AdminApplicationStatuses.REJECTED;
    case ApplicationStatus.ENDED:
    case ApplicationStatus.TERMINATED:
    case ApplicationStatus.ACTIVE:
    case ApplicationStatus.PROCESSED:
      return AdminApplicationStatuses.SIGNED;
    default:
      return AdminApplicationStatuses.NO_ACTION;
  }
}

type GetStatusUpdatedDateParams = Partial<
  Pick<
    ApplicationType,
    'signed_at' | 'rejected_at' | 'processed_at' | 'created_at'
  >
>;

export function getStatusUpdatedDate({
  signed_at,
  rejected_at,
  processed_at,
  created_at,
}: GetStatusUpdatedDateParams): string {
  const statusUpdateDate =
    (signed_at || rejected_at || processed_at || created_at || 0) * 1000;

  return format(statusUpdateDate, 'dd.MM.yyyy HH:mm');
}

type GetCashierParams = Pick<ApplicationType, 'application_reference'>;

export function getCashier({
  application_reference,
}: GetCashierParams): string {
  return application_reference?.creator?.profile
    ? [
        application_reference?.creator?.profile.first_name,
        application_reference?.creator?.profile.last_name,
      ].join(' ')
    : '-';
}

type ApplicationClientNameProps = Pick<
  ApplicationType,
  'user_info' | 'for_private_person' | 'legal_person_info'
>;

export const ApplicationClientName = memo(
  ({
    user_info,
    for_private_person,
    legal_person_info,
  }: ApplicationClientNameProps) => {
    const privatePersonName = user_info
      ? [user_info.first_name, user_info.last_name].join(' ')
      : '-';

    const legalPersonName = user_info
      ? `(${[user_info.first_name, user_info.last_name].join(' ')})`
      : '';

    if (for_private_person) {
      return (
        <Tooltip
          arrowSize={8}
          hasArrow
          label={privatePersonName}
          placement="top"
        >
          <Text as="span" whiteSpace="nowrap">
            {privatePersonName}
          </Text>
        </Tooltip>
      );
    }

    return legal_person_info ? (
      <Tooltip arrowSize={8} hasArrow label={legalPersonName} placement="top">
        <Text as="span">
          {legal_person_info?.name}{' '}
          <Text as="span" whiteSpace="nowrap">
            {legalPersonName}
          </Text>
        </Text>
      </Tooltip>
    ) : (
      <Text as="span">-</Text>
    );
  },
);
ApplicationClientName.displayName = 'ApplicationClient';

export const getPendingRefundRequest = (
  refundRequests: ApplicationDirectPaymentRefunds,
): ApplicationDirectPaymentRefundItem | undefined | null => {
  return refundRequests?.find(
    (refundRequest) => refundRequest?.status === RefundStatuses.PENDING,
  );
};
