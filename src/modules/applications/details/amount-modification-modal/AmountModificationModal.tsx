import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalOverlay,
} from '@chakra-ui/modal';
import { Button, VStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { AMOUNT_MODIFICATION_REASONS } from 'modules/applications/details/amount-modification-modal/AmountModificationModal.constants';
import { generateAmountHint } from 'modules/terminal/Terminal.utils';
import { useEffect } from 'react';
import {
  type Control,
  Controller,
  type FieldValues,
  useForm,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import type { ApplicationScheduleType } from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ControlledSelect,
  ModalCloseButton,
  NumericNumberInput,
  TextInput,
} from 'shared/components';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import {
  useAmountModificationReasonOptions,
  useApplicationModify,
} from './AmountModificationModal.hooks';
import {
  AmountModificationSchema,
  type AmountModificationSchemaType,
} from './AmountModificationModal.schema';

type AmountModificationModalProps = {
  isOpen: boolean;
  applicationId: number;
  initialAmount: number;
  onClose: () => void;
  scheduleType: ApplicationScheduleType;
  minMaxModificationAmount: {
    min: number;
    max: number;
  };
};

export const AmountModificationModal = ({
  isOpen,
  applicationId,
  initialAmount,
  onClose,
  minMaxModificationAmount,
}: AmountModificationModalProps) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);

  const amountHint = generateAmountHint({
    min: minMaxModificationAmount.min,
    max: minMaxModificationAmount.max,
  });

  const {
    control,
    register,
    formState: { errors, isValid },
    setError,
    resetField,
    handleSubmit,
    watch,
  } = useForm<AmountModificationSchemaType>({
    resolver: zodResolver(
      AmountModificationSchema({
        newRequestedAmountMinLength: minMaxModificationAmount.min,
        newRequestedAmountMaxLength: minMaxModificationAmount.max,
        initialAmount,
      }),
    ),
    mode: 'onChange',
    defaultValues: {
      newRequestedAmount: initialAmount,
      reason: AMOUNT_MODIFICATION_REASONS[0],
      otherReason: '',
    },
  });
  const [reason, otherReason] = watch(['reason', 'otherReason']);

  const cancellationReasons = useAmountModificationReasonOptions();

  const { onSubmit, isSubmitting } = useApplicationModify({
    setError,
    applicationId,
    onSuccess: onClose,
  });

  useEffect(() => {
    if (!otherReason) {
      return;
    }

    resetField('otherReason');
  }, [reason]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} scrollBehavior="inside">
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy="application-modify-amount-modal-content"
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>{t('amount-modification-modal.title')}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack align="stretch" gap={2}>
            <Controller
              control={control}
              name="newRequestedAmount"
              render={({ field: { onChange, ...rest } }) => (
                <NumericNumberInput
                  {...rest}
                  autoFocus
                  data-cy="application-modify-amount-new-amount"
                  error={
                    errors.newRequestedAmount
                      ? errors.newRequestedAmount.message === 'required'
                        ? t('common:forms.required')
                        : amountHint
                      : undefined
                  }
                  hint={amountHint}
                  label={t('amount-modification-modal.labels.new-amount')}
                  onValueChange={({ floatValue }) => {
                    onChange(floatValue);
                  }}
                  suffix=" €"
                />
              )}
            />
            <ControlledSelect
              control={control as unknown as Control<FieldValues>}
              data-cy="application-modify-amount-reason"
              items={cancellationReasons}
              label={t('amount-modification-modal.labels.reason')}
              name="reason"
            />
            {reason ===
              LocizeApplicationsKeys.AMOUNT_MODIFICATION_MODAL_REASONS_OTHER && (
              <TextInput
                data-cy="application-cancellation-other-reason"
                label={t('cancellation-modal.labels.other-reason')}
                {...register('otherReason')}
                error={
                  errors.otherReason?.message
                    ? t(`common:forms.${errors.otherReason.message}`)
                    : undefined
                }
              />
            )}
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            display={['none', 'inline-flex']}
            disabled={isSubmitting}
            mr={[0, 3]}
            onClick={onClose}
          >
            {t('common:forms.cancel')}
          </Button>
          <ButtonWithLoader
            data-cy="application-modify-amount-submit"
            disabled={isSubmitting || !isValid}
            loading={isSubmitting}
            type="submit"
          >
            {t(LocizeApplicationsKeys.APPLICATION_UPDATE_FORM_SUBMIT)}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
