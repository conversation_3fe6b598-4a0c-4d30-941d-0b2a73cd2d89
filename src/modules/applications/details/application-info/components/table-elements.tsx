import { type BoxProps } from '@chakra-ui/react';
import { Td, Tr } from '@chakra-ui/table';
import type { PropsWithChildren, ReactNode } from 'react';

export const ApplicationInfoRowTd = ({
  children,
  ...props
}: PropsWithChildren<BoxProps>) => {
  const { translate, ...restProps } = props as any;
  return (
    <Td
      border={'none' as any}
      pl={0 as any}
      pr={6 as any}
      py={2 as any}
      textStyle={'body2' as any}
      {...restProps}
    >
      {children}
    </Td>
  );
};

export const ApplicationInfoRow = ({
  label,
  value,
  onClick,
  withHoverPointer = false,
}: {
  label: string;
  value: ReactNode;
  onClick?: () => void;
  withHoverPointer?: boolean;
}) => (
  <Tr cursor={withHoverPointer ? 'pointer' : undefined} onClick={onClick}>
    <ApplicationInfoRowTd>{label}</ApplicationInfoRowTd>
    <ApplicationInfoRowTd textStyle="body2-highlight">
      {value}
    </ApplicationInfoRowTd>
  </Tr>
);
