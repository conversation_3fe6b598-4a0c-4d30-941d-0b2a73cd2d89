import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ver<PERSON>,
} from '@chakra-ui/modal';
import { Box, Button, Icon, Text } from '@chakra-ui/react';
import { type FC, type RefObject, useCallback, useEffect, useRef } from 'react';
import { type FieldName, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiAlertCircle } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ModalCloseButton,
  NumericNumberInput,
} from 'shared/components';
import { useRequestRefundApplication } from 'shared/hooks/application';
import { MIN_AMOUNT_REFUND_REQUEST } from 'shared/types/application';
import { PaymentProvider } from 'shared/utils/app-settings';

type RefundingModalProps = {
  isOpen: boolean;
  allowedRefundAmount: number;
  applicationId: number;
  initialAmount: number;
  onSuccess: () => void;
  onClose: () => void;
};

export const RefundingModal: FC<RefundingModalProps> = ({
  isOpen,
  ...rest
}) => {
  const inputRef = useRef(null);

  return (
    <Modal
      initialFocusRef={inputRef}
      isOpen={isOpen}
      onClose={rest.onClose}
      scrollBehavior="inside"
    >
      <ModalOverlay display={['none', 'flex']} />
      <RefundRequestModalContent {...rest} inputRef={inputRef} />
    </Modal>
  );
};

type RefundRequestModalContentProps = {
  inputRef: RefObject<HTMLInputElement>;
  applicationId: number;
  initialAmount: number;
  allowedRefundAmount: number;
  onSuccess: () => void;
  onClose: () => void;
};

type RefundRequestForm = {
  refundAmount: number;
};

const AMOUNT_SUFFIX = ' €';

const RefundRequestModalContent: FC<RefundRequestModalContentProps> = ({
  inputRef,
  applicationId,
  initialAmount,
  allowedRefundAmount,
  onSuccess,
  onClose,
}) => {
  const { t, i18n } = useTranslation(['applications', 'common']);
  const MIN_REFUND_AMOUND = MIN_AMOUNT_REFUND_REQUEST;
  const MAX_REFUND_AMOUND =
    Math.round((initialAmount - allowedRefundAmount) * 100) / 100;
  const maxRefundAmountFormattedString = MAX_REFUND_AMOUND.toLocaleString(
    i18n.language,
    {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    },
  );
  const amountHint = `${MIN_REFUND_AMOUND} - ${maxRefundAmountFormattedString}€`;

  const {
    register,
    formState: { errors },
    setError,
    handleSubmit,
  } = useForm<RefundRequestForm>({
    defaultValues: { refundAmount: MAX_REFUND_AMOUND },
  });
  const onFieldValidationError = useCallback(
    (field: string) => {
      setError(
        field as FieldName<RefundRequestForm>,
        { message: 'invalid-format' },
        { shouldFocus: true },
      );
    },
    [setError],
  );

  const { isLoading: isSubmitting, requestRefundApplication } =
    useRequestRefundApplication({ onFieldValidationError });

  useEffect(() => {
    // workaround to wait while input will be initialized by hook form
    setTimeout(() => inputRef.current?.select(), 50);
  }, [inputRef]);

  const paymentProvider = PaymentProvider.KLIX_BANKLINK;
  const onSubmit = useCallback(
    async (formValues: RefundRequestForm) => {
      const refundAmount = +formValues.refundAmount
        .toString()
        .replace(AMOUNT_SUFFIX, '');
      const result = await requestRefundApplication({
        applicationId,
        paymentProvider,
        refundAmount,
      });
      if (result) {
        onSuccess();
      }
    },
    [onSuccess, requestRefundApplication, applicationId, paymentProvider],
  );

  return (
    <ModalContent
      as="form"
      data-cy="application-refund-modal-content"
      maxW="29rem"
      onSubmit={handleSubmit(onSubmit)}
    >
      <ModalHeader>{t('refunding-modal.title')}</ModalHeader>
      <ModalCloseButton />
      <ModalBody>
        <Text mb={4} textStyle="body2">
          {t('refunding-modal.description')}
        </Text>
        <NumericNumberInput
          data-cy="application-refund-modal-amount"
          {...register('refundAmount', {
            required: 'required',
            min: { value: MIN_REFUND_AMOUND, message: 'min-value' },
            max: { value: MAX_REFUND_AMOUND, message: 'max-value' },
          })}
          error={
            errors.refundAmount?.message
              ? errors.refundAmount.message === 'required'
                ? t('common:forms.required')
                : amountHint
              : undefined
          }
          hint={amountHint}
          label={t('refund-request-modal.labels.refund-amount')}
          suffix={AMOUNT_SUFFIX}
        />
        <Box
          alignItems="center"
          bg="red.100"
          borderRadius="4px"
          data-cy="application-refund-modal-disclaimer"
          display="flex"
          mt={[3, 5]}
          px={3}
          py={2}
        >
          <Icon as={FiAlertCircle} boxSize={5} color="red.700" mr={2} />
          <Text color="red.900" textStyle="body2">
            {t('refund-request-modal.disclaimer')}
          </Text>
        </Box>
      </ModalBody>

      <ModalFooter>
        <Button
          colorScheme={ColorSchemes.SECONDARY}
          display={['none', 'inline-flex']}
          disabled={isSubmitting}
          mr={[0, 3]}
          onClick={onClose}
        >
          {t('common:forms.cancel')}
        </Button>
        <ButtonWithLoader
          data-cy="application-refund-modal-submit"
          disabled={isSubmitting}
          loading={isSubmitting}
          type="submit"
        >
          {t('refund-request-modal.submit')}
        </ButtonWithLoader>
      </ModalFooter>
    </ModalContent>
  );
};
