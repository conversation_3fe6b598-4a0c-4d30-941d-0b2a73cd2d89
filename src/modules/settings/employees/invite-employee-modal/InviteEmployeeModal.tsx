import { Checkbox } from '@chakra-ui/checkbox';
import {
  <PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
} from '@chakra-ui/modal';
import { Box, Button, Text, VStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { toggleEmployeeInviteModal } from 'modules/settings/employees/invite-employee-modal/InviteEmployeeModal.model';
import { useCallback, useState } from 'react';
import { type FieldName, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiUser } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ModalCloseButton,
  TextInput,
} from 'shared/components';
import { useShowError, useShowMessage } from 'shared/hooks/alerts';
import { useSendMerchantUserInvite } from 'shared/hooks/merchant';
import { MerchantPermissions } from 'shared/types';
import { z } from 'zod';

const InviteEmployeeSchema = z.object({
  email: z.string().email('invalid-format'),
});

type InviteEmployeeSchemaType = z.infer<typeof InviteEmployeeSchema>;

type Props = {
  open: boolean;
};

export const InviteEmployeeModal = ({ open }: Props) => {
  const { t } = useTranslation(['settings', 'common']);
  const showError = useShowError();
  const showMessage = useShowMessage();

  const {
    register,
    formState: { errors },
    handleSubmit,
    setError,
  } = useForm<InviteEmployeeSchemaType>({
    defaultValues: { email: '' },
    resolver: zodResolver(InviteEmployeeSchema),
  });
  const [permissionBits, setPermissionBits] = useState<number>(
    MerchantPermissions.Cashier,
  );

  const onClose = useCallback(() => {
    toggleEmployeeInviteModal(false);
  }, []);

  const onFieldValidationError = useCallback(
    (field: string) => {
      setError(
        field as FieldName<InviteEmployeeSchemaType>,
        { message: 'invalid-format' },
        { shouldFocus: true },
      );
    },
    [setError],
  );
  const { isLoading, sendMerchantUserInvite } = useSendMerchantUserInvite({
    onFieldValidationError,
  });

  const onSubmit = useCallback(
    async (formValues: InviteEmployeeSchemaType) => {
      if (permissionBits === 0) {
        showError(t('notifications.invite-employee-no-roles-error'));
        return;
      }

      const result = await sendMerchantUserInvite({
        ...formValues,
        permissionBits,
      });
      if (result !== null) {
        onClose();
        showMessage(t('notifications.employee-invited'), FiUser);
      }
    },
    [
      permissionBits,
      sendMerchantUserInvite,
      onClose,
      showError,
      t,
      showMessage,
    ],
  );

  return (
    <Modal isOpen={open} onClose={onClose} scrollBehavior="inside">
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy="invite-employee-modal-content"
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>{t('employees.modal.title')}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={[4, 1]}>
          <TextInput
            autoFocus
            data-cy="invite-employee-email-input"
            inputMode="text"
            label={t('employees.modal.email')}
            mb={3}
            {...register('email')}
            error={
              errors.email?.message
                ? t(`common:forms.${errors.email.message}`)
                : undefined
            }
            isDisabled={isLoading}
          />
          <Text textStyle="body1">{t('employees.modal.roles-title')}</Text>
          <VStack gap={[2, 0]}>
            <RoleCheckbox
              description={t('employees.role-descriptions.cashier')}
              isDisabled={isLoading}
              onChange={setPermissionBits}
              permissionBits={permissionBits}
              role={MerchantPermissions.Cashier}
              title={t('employees.roles.cashier')}
            />
            <RoleCheckbox
              description={t('employees.role-descriptions.accountant')}
              isDisabled={isLoading}
              onChange={setPermissionBits}
              permissionBits={permissionBits}
              role={MerchantPermissions.Accountant}
              title={t('employees.roles.accountant')}
            />
            <RoleCheckbox
              description={t('employees.role-descriptions.developer')}
              isDisabled={isLoading}
              onChange={setPermissionBits}
              permissionBits={permissionBits}
              role={MerchantPermissions.Developer}
              title={t('employees.roles.developer')}
            />
            <RoleCheckbox
              description={t('employees.role-descriptions.merchant-admin')}
              isDisabled={isLoading}
              onChange={setPermissionBits}
              permissionBits={permissionBits}
              role={MerchantPermissions.Admin}
              title={t('employees.roles.merchant-admin')}
            />
          </VStack>
        </ModalBody>

        <ModalFooter
          borderTop={['1px solid', 'none']}
          borderTopColor="neutral.150"
        >
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            display={['none', 'inline-flex']}
            disabled={isLoading}
            mr={[0, 3]}
            mt={[3, 0]}
            onClick={onClose}
          >
            {t('common:forms.cancel')}
          </Button>
          <ButtonWithLoader
            data-cy="employees-settings-modal-submit"
            disabled={isLoading}
            loading={isLoading}
            type="submit"
          >
            {t('employees.modal.submit')}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

type RoleCheckboxProps = {
  role: MerchantPermissions;
  title: string;
  description: string;
  permissionBits: number;
  isDisabled?: boolean;
  onChange: (newPermissions: number) => void;
};

const RoleCheckbox = ({
  role,
  title,
  permissionBits,
  isDisabled,
  description,
  onChange,
}: RoleCheckboxProps): JSX.Element => {
  return (
    <Box alignItems="flex-start" display="flex" py="0.375rem" w="100%">
      <Checkbox
        data-cy={`invite-employee-role-${role}`}
        flexShrink={0}
        isChecked={Boolean(permissionBits & role)}
        disabled={isDisabled}
        mt="0.125rem"
        onChange={() => {
          onChange(permissionBits ^ role);
        }}
      />
      <Box flexGrow={1} ml={3}>
        <Text textStyle="body1-highlight">{title}</Text>
        <Text textStyle="body2">{description}</Text>
      </Box>
    </Box>
  );
};
