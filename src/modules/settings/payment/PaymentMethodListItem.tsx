import { Box, Switch, Text } from '@chakra-ui/react';
import { PaymentPlanLogo } from 'modules/terminal/components/payment-plan';
import type { ApplicationScheduleType } from 'shared/api';
import type { PaymentMethodInfo } from 'shared/hooks/payment-plan';

export type PaymentMethodListItemProps = {
  method: ApplicationScheduleType;
  mainDescription: string;
  secondaryDescription: string;
  shouldDisable: boolean;
  methodInfo: PaymentMethodInfo;
};

export const PaymentMethodListItem = ({
  method,
  mainDescription,
  secondaryDescription,
  shouldDisable,
  methodInfo,
}: PaymentMethodListItemProps) => {
  return (
    <Box
      backgroundColor={methodInfo.isEnabled ? 'primary.100' : 'white'}
      border="1px solid"
      borderColor="neutral.100"
      borderRadius="4px"
      data-cy={`payment-method-${method}`}
      data-enabled={methodInfo.isEnabled}
      maxW="400px"
      px={4}
      py={3}
      w="100%"
    >
      <Box
        alignItems="center"
        borderBottom="1px"
        borderBottomColor={methodInfo.isEnabled ? 'primary.200' : 'neutral.100'}
        className="top-container"
        display="flex"
        mb={3}
        pb={4}
      >
        <Box mr={3}>
          <PaymentPlanLogo
            isSelected={methodInfo.isEnabled}
            logoUrl={methodInfo.logoUrl}
            name={methodInfo.name}
          />
        </Box>
        <Box flexGrow={1}>
          <Text textStyle="body1-highlight">{methodInfo.name}</Text>
          {!!methodInfo.period && (
            <Text textStyle="caption">{methodInfo.period.text}</Text>
          )}
        </Box>
        <Switch.Root checked={methodInfo.isEnabled} disabled={shouldDisable}>
          <Switch.HiddenInput />
          <Switch.Control>
            <Switch.Thumb />
          </Switch.Control>
        </Switch.Root>
      </Box>
      <Text mb={secondaryDescription ? 2 : 0} textStyle="body2">
        {mainDescription}
      </Text>
      {!!secondaryDescription && (
        <Text textStyle="body2-highlight">{secondaryDescription}</Text>
      )}
    </Box>
  );
};
