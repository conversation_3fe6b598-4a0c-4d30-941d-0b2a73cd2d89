import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
} from '@chakra-ui/modal';
import { Button, VStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback } from 'react';
import { type DefaultValues, type FieldName, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ModalCloseButton,
  NumericNumberInput,
  TextInput,
} from 'shared/components';
import { useShowError } from 'shared/hooks/alerts';
import { useUpdateMerchant } from 'shared/hooks/merchant';
import { z } from 'zod';

const MerchantDataUpdateSchema = z.object({
  name: z.string().min(1, 'required'),
  registry_code: z.string().min(1, 'required'),
  iban: z.string().min(1, 'required'),
  beneficiary_name: z.string().min(1, 'required'),
  email: z.string().min(1, 'required').email('invalid-format'),
  phone: z.string().min(1, 'required'),
  address: z.string().min(1, 'required'),
});

export type MerchantDataUpdateSchemaType = z.infer<
  typeof MerchantDataUpdateSchema
>;

type Props = {
  isOpen: boolean;
  defaultValues: DefaultValues<MerchantDataUpdateSchemaType>;
  onSuccess: () => void;
  onClose: () => void;
};

export const MerchantInfoModal = ({
  isOpen,
  defaultValues,
  onClose,
  onSuccess,
}: Props) => {
  const { t } = useTranslation(['settings', 'common']);
  const showError = useShowError();
  const {
    register,
    formState: { errors },
    handleSubmit,
    setError,
  } = useForm<MerchantDataUpdateSchemaType>({
    defaultValues,
    resolver: zodResolver(MerchantDataUpdateSchema),
  });

  const onFieldValidationError = useCallback(
    (field: string) => {
      setError(
        field as FieldName<MerchantDataUpdateSchemaType>,
        { message: 'invalid-format' },
        { shouldFocus: true },
      );
    },
    [setError],
  );

  const { isLoading: isSubmitting, updateMerchant } = useUpdateMerchant({
    onFieldValidationError,
  });

  const onSubmit = useCallback(
    async (formValues: MerchantDataUpdateSchemaType) => {
      try {
        const { iban, beneficiary_name } = defaultValues;
        await updateMerchant({
          ...formValues,
          ...(iban && beneficiary_name && { iban, beneficiary_name }),
        });

        onSuccess();
      } catch {
        showError(
          "Can't perform Merchant data update. Please try again later.",
        );
      }
    },
    [defaultValues, updateMerchant, onSuccess, showError],
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} scrollBehavior="inside">
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy="merchant-settings-modal-content"
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>{t('merchant.modal.title')}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={[4, 1]}>
          <VStack gap={1}>
            <TextInput
              autoFocus
              data-cy="merchant-settings-modal-name"
              inputMode="text"
              label={t('merchant.labels.name')}
              {...register('name')}
              error={
                errors.name?.message
                  ? t(`common:forms.${errors.name.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
            <NumericNumberInput
              data-cy="merchant-settings-modal-registry-code"
              label={t('merchant.labels.registry-code')}
              {...register('registry_code')}
              defaultValue={defaultValues.registry_code}
              disabled={isSubmitting}
              error={
                errors.registry_code?.message
                  ? t(`common:forms.${errors.registry_code.message}`)
                  : undefined
              }
            />
            <TextInput
              data-cy="merchant-settings-modal-email"
              inputMode="email"
              label={t('merchant.labels.email')}
              {...register('email')}
              error={
                errors.email?.message
                  ? t(`common:forms.${errors.email.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
            <TextInput
              data-cy="merchant-settings-modal-phone"
              inputMode="tel"
              label={t('merchant.labels.phone')}
              {...register('phone')}
              error={
                errors.phone?.message
                  ? t(`common:forms.${errors.phone.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
            <TextInput
              data-cy="merchant-settings-modal-address"
              inputMode="text"
              label={t('merchant.labels.address')}
              {...register('address')}
              error={
                errors.address?.message
                  ? t(`common:forms.${errors.address.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
            <TextInput
              data-cy="merchant-settings-modal-iban"
              inputMode="text"
              label={t('merchant.labels.iban')}
              {...register('iban')}
              error={
                errors.iban?.message
                  ? t(`common:forms.${errors.iban.message}`)
                  : undefined
              }
              isDisabled
            />
            <TextInput
              data-cy="merchant-settings-modal-beneficiary-name"
              inputMode="text"
              label={t('merchant.labels.beneficiary-name')}
              {...register('beneficiary_name')}
              error={
                errors.beneficiary_name?.message
                  ? t(`common:forms.${errors.beneficiary_name.message}`)
                  : undefined
              }
              isDisabled
            />
          </VStack>
        </ModalBody>

        <ModalFooter
          borderTop={['1px solid', 'none']}
          borderTopColor="neutral.150"
        >
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            display={['none', 'inline-flex']}
            disabled={isSubmitting}
            mr={[0, 3]}
            mt={[3, 0]}
            onClick={onClose}
          >
            {t('common:forms.cancel')}
          </Button>
          <ButtonWithLoader
            data-cy="merchant-settings-modal-submit"
            disabled={isSubmitting}
            loading={isSubmitting}
            type="submit"
          >
            {t('merchant.modal.submit')}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
