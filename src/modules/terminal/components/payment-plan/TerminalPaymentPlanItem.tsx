import { Box, useBreakpointValue } from '@chakra-ui/react';
import { PaymentPlanCard } from 'modules/terminal/components/payment-plan';
import {
  PaymentPlanDesktopPopover,
  PaymentPlanMobilePopover,
} from 'modules/terminal/components/payment-plan/PaymentPlanPopover';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';

import type {
  TerminalCreditSettingsPeriodInfo,
  TerminalPaymentPlan,
} from '../../Terminal.types';

type PaymentPlanStepItemProps = {
  plan: TerminalPaymentPlan;
  dataCy?: string;
  isSelected: boolean;
  creditSettingsPeriodInfo: Nullable<Array<TerminalCreditSettingsPeriodInfo>>;
  isCreditSettingsLoading: boolean;
};

export const TerminalPaymentPlanItem = ({
  plan,
  dataCy,
  isSelected,
  creditSettingsPeriodInfo,
  isCreditSettingsLoading,
}: PaymentPlanStepItemProps) => {
  const isMobile = useBreakpointValue({ base: true, lg: false });

  const {
    watch,
    formState: { isSubmitting },
    setValue,
  } = useFormContext<TerminalFormSchemaType>();
  const [isPracticeMode, amount] = watch(['isPracticeMode', 'amount']);

  const onClick = useCallback(() => {
    setValue('scheduleType', plan.scheduleType, {
      shouldValidate: true,
    });
    setValue('amount', amount, {
      shouldValidate: true,
    });
  }, [amount, plan.scheduleType, setValue]);

  if (isMobile) {
    return (
      <PaymentPlanCard
        dataCy={dataCy}
        isDisabled={isSubmitting}
        isPracticeMode={isPracticeMode}
        isSelected={isSelected}
        onClick={onClick}
        plan={plan}
        applicationAmount={amount}
        rightContent={
          !!isSelected &&
          !!(creditSettingsPeriodInfo || isCreditSettingsLoading) ? (
            <PaymentPlanMobilePopover
              creditSettingsPeriodInfo={creditSettingsPeriodInfo ?? null}
              dataCy={`${dataCy}-popover`}
              isPracticeMode={isPracticeMode}
              scheduleType={plan.scheduleType}
            />
          ) : undefined
        }
      />
    );
  }

  return (
    <PaymentPlanDesktopPopover
      creditSettingsPeriodInfo={creditSettingsPeriodInfo ?? null}
      dataCy={`${dataCy}-popover`}
      isLoading={isCreditSettingsLoading}
      isOpen={
        !!isSelected && !!(creditSettingsPeriodInfo || isCreditSettingsLoading)
      }
      isPracticeMode={isPracticeMode}
      scheduleType={plan.scheduleType}
    >
      <Box>
        <PaymentPlanCard
          dataCy={dataCy}
          isDisabled={isSubmitting}
          isPracticeMode={isPracticeMode}
          applicationAmount={amount}
          isSelected={isSelected}
          onClick={onClick}
          plan={plan}
        />
      </Box>
    </PaymentPlanDesktopPopover>
  );
};
