import {
  Popover,
  <PERSON>overArrow,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
} from '@chakra-ui/popover';
import { Center, Icon, IconButton, Spinner } from '@chakra-ui/react';
import { PaymentInfo } from 'modules/terminal/components/payment-plan/PaymentInfo';
import type { TerminalCreditSettingsPeriodInfo } from 'modules/terminal/Terminal.types';
import type { PropsWithChildren } from 'react';
import { FiInfo } from 'react-icons/fi';
import type { ApplicationScheduleType } from 'shared/api';

type PopperModifier = {
  name: string;
  options?: Record<string, unknown>;
  enabled?: boolean;
};

type PaymentPlanMobilePopoverProps = {
  scheduleType: ApplicationScheduleType;
  creditSettingsPeriodInfo: Nullable<Array<TerminalCreditSettingsPeriodInfo>>;
  isPracticeMode?: boolean;
  dataCy?: string;
  isLoading?: boolean;
};

export const PaymentPlanMobilePopover = ({
  scheduleType,
  creditSettingsPeriodInfo,
  isPracticeMode,
  dataCy,
  isLoading,
}: PaymentPlanMobilePopoverProps) => {
  return (
    <Popover
      arrowSize={4}
      autoFocus={false}
      data-cy={dataCy}
      modifiers={
        [
          {
            name: 'offset',
            options: {
              offset: [0, 0],
            },
          },
        ] satisfies PopperModifier[]
      }
      placement="left-start"
      variant="info"
    >
      <PopoverTrigger>
        <IconButton
          _active={{ bg: 'transparent' }}
          _focus={{ bg: 'transparent' }}
          _hover={{ bg: 'transparent' }}
          aria-label="calculated-prices"
          as="a"
          boxSize={12}
          color={isPracticeMode ? 'neutral.900' : 'primary.800'}
          flexShrink={0}
          mr={-2}
          my={-2}
          onClick={(e) => {
            e.stopPropagation();
          }}
          variant="ghost"
        >
          <Icon as={FiInfo} boxSize={6} />
        </IconButton>
      </PopoverTrigger>
      <PopoverContent bg="neutral.900" px={3} py={2}>
        <PopoverArrow bg="neutral.900" />
        <PopoverBody p={0}>
          {isLoading ? (
            <Center h="100%" w="100%">
              <Spinner
                boxSize={5}
                color={isPracticeMode ? 'neutral.900' : 'primary.800'}
              />
            </Center>
          ) : (
            <PaymentInfo
              creditSettingsPeriodInfo={creditSettingsPeriodInfo}
              isPracticeMode={isPracticeMode}
              scheduleType={scheduleType}
            />
          )}
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};

type PaymentPlanDesktopPopoverProps = {
  scheduleType: ApplicationScheduleType;
  creditSettingsPeriodInfo: Nullable<Array<TerminalCreditSettingsPeriodInfo>>;
  isOpen: boolean;
  isPracticeMode?: boolean;
  dataCy?: string;
  isLoading?: boolean;
};

export const PaymentPlanDesktopPopover = ({
  children,
  scheduleType,
  isOpen,
  isPracticeMode,
  dataCy,
  creditSettingsPeriodInfo,
  isLoading,
}: PropsWithChildren<PaymentPlanDesktopPopoverProps>) => {
  const bgColor = isPracticeMode ? 'neutral.100' : 'primary.100';

  return (
    <Popover
      arrowSize={8}
      autoFocus={false}
      isOpen={isOpen}
      modifiers={
        [
          {
            name: 'offset',
            options: {
              offset: [0, 12],
            },
          },
          {
            name: 'flip',
            enabled: false,
          },
        ] satisfies PopperModifier[]
      }
      placement="right-start"
      variant="info"
    >
      <PopoverTrigger>{children}</PopoverTrigger>
      <PopoverContent bg={bgColor} data-cy={dataCy} px={5} py={3}>
        <PopoverArrow bg={bgColor} />
        <PopoverBody p={0}>
          {isLoading ? (
            <Center h="100%" w="100%">
              <Spinner
                boxSize={5}
                color={isPracticeMode ? 'neutral.900' : 'primary.800'}
              />
            </Center>
          ) : (
            <PaymentInfo
              creditSettingsPeriodInfo={creditSettingsPeriodInfo}
              isDesktop
              isPracticeMode={isPracticeMode}
              scheduleType={scheduleType}
            />
          )}
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};
