import { Box, VStack } from '@chakra-ui/react';
import { TerminalStepWrapper } from 'modules/terminal/components/shared';
import { usePurchaseDetailsStep } from 'modules/terminal/components/steps/purchase-details/PurchaseDetailsStep.hooks';
import { StoreTypes } from 'modules/terminal/Terminal.types';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { AppTooltip, NumericNumberInput, TextInput } from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { region } from 'shared/lib';
import { AppRegions } from 'shared/utils';

export const PurchaseDetailsStep = () => {
  const { t } = useTranslation('terminal');
  const {
    merchantDownPaymentHint,
    isRequiredMerchantDownPayment,
    control,
    register,
    amountHint,
    errors,
    isSubmitting,
    storeType,
    isVisible,
    isStepCompleted,
    isValidLoanAmountByServer,
  } = usePurchaseDetailsStep();

  const amountLabel =
    storeType === StoreTypes.PHYSICAL && region === AppRegions.LV
      ? t(`purchase-details.amount.lv-label-${storeType}`)
      : t(`purchase-details.amount.label-${storeType}`);

  return (
    <TerminalStepWrapper
      dataCy={CypressTerminalKeys.PURCHASE_DETAILS_STEP}
      isCompleted={isStepCompleted}
      isVisible={isVisible}
      notCompletedEl={t('purchase-details.not-completed-text')}
      stepNo={3}
      title={t('purchase-details.title')}
    >
      <VStack gap={3}>
        <Controller
          control={control}
          name="amount"
          render={({
            field: { onChange, ...rest },
            fieldState: { error, isTouched },
          }) => {
            return (
              <NumericNumberInput
                {...rest}
                allowNegative={false}
                data-cy={CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT}
                disabled={isSubmitting}
                error={
                  isTouched
                    ? error?.message
                      ? error.message === 'required'
                        ? t('common:forms.required')
                        : amountHint
                      : !isValidLoanAmountByServer
                        ? amountHint
                        : undefined
                    : undefined
                }
                hint={amountHint}
                label={
                  <Box alignItems="center" display="flex">
                    {amountLabel}
                    {!isRequiredMerchantDownPayment &&
                      storeType === StoreTypes.PHYSICAL && (
                        <AppTooltip
                          isInfo
                          label={t('purchase-details.amount.tooltip-text')}
                          ml={1}
                        />
                      )}
                  </Box>
                }
                onValueChange={({ floatValue }) => {
                  onChange(floatValue);
                }}
                suffix=" €"
              />
            );
          }}
        />
        {!!isRequiredMerchantDownPayment && (
          <Controller
            control={control}
            name="merchantDownPayment"
            render={({
              field: { onChange, ...rest },
              fieldState: { error },
            }) => (
              <NumericNumberInput
                {...rest}
                allowNegative={false}
                data-cy={
                  CypressTerminalKeys.PURCHASE_DETAILS_DOWN_PAYMENT_INPUT
                }
                disabled={isSubmitting}
                error={
                  error?.message
                    ? error.message === 'required'
                      ? t('common:forms.required')
                      : merchantDownPaymentHint
                    : undefined
                }
                hint={merchantDownPaymentHint}
                label={t('purchase-details.merchant-down-payment')}
                onValueChange={({ floatValue }) => {
                  onChange(floatValue);
                }}
                suffix=" €"
              />
            )}
          />
        )}
        <TextInput
          data-cy={CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT}
          label={t('purchase-details.order-reference.label')}
          {...register('reference')}
          error={
            errors.reference?.message
              ? t(`common:forms.${errors.reference?.message}`)
              : undefined
          }
          hint={t('purchase-details.order-reference.hint')}
          isDisabled={isSubmitting}
        />
      </VStack>
    </TerminalStepWrapper>
  );
};
