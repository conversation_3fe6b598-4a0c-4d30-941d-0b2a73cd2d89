import { HStack, Text } from '@chakra-ui/react';
import { Fade } from '@chakra-ui/transition';
import { TerminalStepWrapper } from 'modules/terminal/components/shared';
import { usePersonalInfoStep } from 'modules/terminal/components/steps/personal-info/PersonalInfoStep.hooks';
import { StoreTypes } from 'modules/terminal/Terminal.types';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ControlledToggle,
  PatternNumberInput,
  TextInput,
  ToggleItem,
} from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { availableLanguages } from 'shared/lib';
import { PHONE_FORMAT } from 'shared/utils';

export const PersonalInfoStep = () => {
  const { t } = useTranslation([
    LocizeNamespaces.TERMINAL,
    LocizeNamespaces.COMMON,
  ]);
  const {
    isVisible,
    isSubmitting,
    register,
    control,
    isPracticeMode,
    storeType,
  } = usePersonalInfoStep();

  return (
    <TerminalStepWrapper
      dataCy={CypressTerminalKeys.PERSONAL_INFO_STEP}
      isCompleted
      isVisible={isVisible}
      stepNo={4}
      subtitle={t('personal-information.disclaimer')}
      title={t('personal-information.title')}
    >
      <Fade in={storeType === StoreTypes.ONLINE} unmountOnExit>
        <HStack mb={3} gap={3} w="100%">
          <TextInput
            data-cy={CypressTerminalKeys.PERSONAL_INFO_FIRST_NAME_INPUT}
            label={t('personal-information.first-name.label')}
            {...register('firstName')}
            isDisabled={isSubmitting}
          />
          <TextInput
            data-cy={CypressTerminalKeys.PERSONAL_INFO_LAST_NAME_INPUT}
            label={t('personal-information.last-name.label')}
            {...register('lastName')}
            isDisabled={isSubmitting}
          />
        </HStack>
        <Controller
          control={control}
          name="personalInfoEmail"
          render={({ field, fieldState: { error } }) => (
            <TextInput
              data-cy={CypressTerminalKeys.PERSONAL_INFO_EMAIL_INPUT}
              error={
                error?.message ? t(`common:forms.${error.message}`) : undefined
              }
              inputMode="email"
              disabled={isSubmitting}
              label={t('personal-information.email.label')}
              {...field}
            />
          )}
        />
        <Controller
          control={control}
          name="personalInfoPhone"
          render={({ field: { onChange, ...rest } }) => (
            <PatternNumberInput
              allowEmptyFormatting
              data-cy={CypressTerminalKeys.PERSONAL_INFO_PHONE_NUMBER_INPUT}
              disabled={isSubmitting}
              format={PHONE_FORMAT}
              label={t('personal-information.phone.label')}
              onValueChange={({ value }) => {
                onChange(value);
              }}
              type="tel"
              {...rest}
            />
          )}
        />
      </Fade>
      <Text my={2} textStyle="body1">
        {t('personal-information.language.label')}
      </Text>
      <ControlledToggle
        control={control}
        data-cy={CypressTerminalKeys.PERSONAL_INFO_LANGUAGE_SELECTOR}
        disabled={isSubmitting}
        name="language"
        w="100%"
      >
        {availableLanguages.map((lang) => (
          <ToggleItem
            colorScheme={
              isPracticeMode ? ColorSchemes.NEUTRAL : ColorSchemes.PRIMARY
            }
            dataCy={`${CypressTerminalKeys.PERSONAL_INFO_LANGUAGE_SELECTOR_OPTION}-${lang}`}
            key={lang}
            value={lang}
          >
            {t(`common:language.${lang}`)}
          </ToggleItem>
        ))}
      </ControlledToggle>
    </TerminalStepWrapper>
  );
};
