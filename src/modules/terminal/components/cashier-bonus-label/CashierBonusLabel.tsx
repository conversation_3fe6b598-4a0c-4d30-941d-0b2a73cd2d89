import { Box, Text, useBreakpointValue, useToken } from '@chakra-ui/react';
import { useTheme } from '@chakra-ui/system';
import { transparentize } from '@chakra-ui/theme-tools';
import { isInteger } from 'lodash';
import { useTranslation } from 'react-i18next';
import { StarIcon } from 'shared/components';
import { useFormattedAmount } from 'shared/hooks/utils';

type CashierBonusLabelProps = {
  applicationAmount?: number;
  cashierBonusPct?: number;
};

export const CashierBonusLabel = ({
  applicationAmount,
  cashierBonusPct,
}: CashierBonusLabelProps) => {
  const theme = useTheme();
  const green600 = useToken('colors', 'green.600');
  const isMobile = useBreakpointValue([true, false]);
  const { t } = useTranslation('terminal');

  const shouldShowBonusPercentage = !applicationAmount && !!cashierBonusPct;
  const shouldShowBonusAmount = !!applicationAmount && !!cashierBonusPct;

  const amountToFormat =
    ((applicationAmount ?? 0) * (cashierBonusPct ?? 0)) / 100;

  const amount = useFormattedAmount(
    ((applicationAmount ?? 0) * (cashierBonusPct ?? 0)) / 100,
    { currency: '', fractionDigits: isInteger(amountToFormat) ? 0 : 2 },
  );

  const bonusPercentageText = isMobile
    ? `${cashierBonusPct} %`
    : t('cashier-bonus.percentage-label', {
        percentage: cashierBonusPct,
      });

  const bonusAmountText = isMobile
    ? `${amount} €`
    : t('cashier-bonus.amount-label', { amount });

  const shouldShowDefaultText =
    !shouldShowBonusPercentage && !shouldShowBonusAmount;

  return (
    <Box
      display="flex"
      gap={1.5}
      px={3}
      py={2}
      bg={transparentize(green600, 0.12)(theme)}
      borderRadius="md"
    >
      <StarIcon my="auto" color={green600} />

      <Text textStyle="body2">
        {shouldShowBonusPercentage && bonusPercentageText}

        {shouldShowBonusAmount && bonusAmountText}

        {shouldShowDefaultText && t('cashier-bonus.label')}
      </Text>
    </Box>
  );
};
