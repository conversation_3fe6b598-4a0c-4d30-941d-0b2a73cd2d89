import { Box, Center, Text, VStack } from '@chakra-ui/react';
import { SlideFade } from '@chakra-ui/transition';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import type { ReactNode } from 'react';
import { useFormContext } from 'react-hook-form';

type TerminalStepWrapperProps = {
  stepNo: number;
  title: string;
  subtitle?: string;
  isCompleted: boolean;
  isVisible?: boolean;
  notCompletedEl?: ReactNode;
  children: ReactNode;
  dataCy?: string;
};

export const TerminalStepWrapper = ({
  stepNo,
  title,
  subtitle,
  children,
  isCompleted,
  isVisible,
  notCompletedEl,
  dataCy,
}: TerminalStepWrapperProps) => {
  const { watch } = useFormContext<TerminalFormSchemaType>();
  const isPracticeMode = watch('isPracticeMode');

  return (
    <SlideFade in={isVisible} unmountOnExit>
      <VStack
        alignItems="stretch"
        data-cy={dataCy}
        display="flex"
        w={['100%', 'auto']}
      >
        <Box alignItems="flex-start" display="flex" mb={subtitle ? [3, 2] : 5}>
          <Center
            bg={isPracticeMode ? 'neutral.800' : 'primary.600'}
            borderRadius="full"
            boxSize={6}
            color="white"
            flexShrink={0}
            mr={[2, 3]}
            mt={['2px', '3px']}
            textStyle="body1-highlight"
          >
            {stepNo}
          </Center>
          <Text textStyle="h3" width={['100%', '400px']}>
            {title}
          </Text>
        </Box>

        <Box ml={[0, 9]} width={['100%', '400px']}>
          {!!subtitle && (
            <Text mb={4} textStyle="body1">
              {subtitle}
            </Text>
          )}
          {children}
          {!isCompleted && !!notCompletedEl && (
            <Text
              bg="neutral.50"
              borderRadius="4px"
              mt={[12, 14]}
              px={3}
              py={2}
              textAlign="center"
              textStyle="body2"
            >
              {notCompletedEl}
            </Text>
          )}
        </Box>
      </VStack>
    </SlideFade>
  );
};
