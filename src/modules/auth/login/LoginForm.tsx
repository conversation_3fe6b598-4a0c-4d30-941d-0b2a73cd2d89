import { Box, Icon, IconButton, Text } from '@chakra-ui/react';
import { LoginMethodSelect } from 'modules/auth/login/components/LoginMethodSelect';
import { useCallback, useContext, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { HiArrowLeft } from 'react-icons/hi';
import { Link, useNavigate } from 'react-router-dom';
import { LoggedOutLayout } from 'shared/components';
import { DesktopHeader, MobileHeader } from 'shared/components/login/Headers';
import {
  LoginRoute,
  SignUpRoute,
  useQueryParams,
} from 'shared/constants/routes';
import { useShowError } from 'shared/hooks/alerts';
import { GlobalStateContext } from 'shared/hooks/state';
import { useEffectOnce } from 'shared/hooks/utility';
import { LoginMethods } from 'shared/types';
import { isEnumValue } from 'shared/utils';

import { useLoginSuccess } from './hooks';
import { IdCardLogin } from './modules/id-card-login/IdCardLogin';
import { MobileIdLogin } from './modules/mobile-id-login/MobileIdLogin';
import { PasswordLogin } from './modules/password-login/PasswordLogin';
import { SmartIdLogin } from './modules/smart-id-login/SmartIdLogin';

export const LoginForm = () => {
  const { isVisitedInvitePage } = useContext(GlobalStateContext);
  const navigate = useNavigate();
  const query = useQueryParams();

  const { t } = useTranslation('login');
  const showError = useShowError();
  const selectedMethod = useMemo(
    () => query.method as LoginMethods | undefined,
    [query.method],
  );

  const redirectUrl = useMemo(() => {
    const url = query.redirectUrl;
    if (typeof url === 'string') {
      return url;
    }

    return '/';
  }, [query]);

  const { isAfterLoginSetupInProgress, onLoginSuccess } = useLoginSuccess({
    redirectUrl,
    inviteHash: isVisitedInvitePage,
  });

  const LoginMethodComponent = useMemo(() => {
    switch (selectedMethod) {
      case LoginMethods.MobileId:
        return MobileIdLogin;
      case LoginMethods.SmartId:
        return SmartIdLogin;
      case LoginMethods.IdCard:
        return IdCardLogin;
      case LoginMethods.Password:
        return PasswordLogin;
      default:
        return () => null;
    }
  }, [selectedMethod]);

  const handleLoginMethodChange = useCallback(
    (method: LoginMethods) => {
      navigate(LoginRoute.format({ method, redirectUrl }));
    },
    [redirectUrl, navigate],
  );

  useEffect(() => {
    if (selectedMethod == null || !isEnumValue(LoginMethods)(selectedMethod)) {
      navigate(
        LoginRoute.format({ redirectUrl, method: LoginMethods.MobileId }),
        {
          replace: true,
        },
      );
    }
  }, [selectedMethod, navigate, redirectUrl]);

  // show toast with error and make replace route without sessionExpired query param
  useEffectOnce(() => {
    if (query.sessionExpired) {
      showError(t('errors.session-expired'));
      navigate(
        LoginRoute.format({ redirectUrl, method: LoginMethods.MobileId }),
        {
          replace: true,
        },
      );
    }
  });

  return (
    <LoggedOutLayout
      desktopHeader={DesktopHeader}
      isLoading={isAfterLoginSetupInProgress}
      mobileHeader={MobileHeader}
    >
      <Box pb={12} pt={[6, '150px']}>
        <Box position="relative">
          {!!isVisitedInvitePage && (
            <Link to={SignUpRoute.format({ inviteHash: isVisitedInvitePage })}>
              <IconButton
                aria-label="back"
                bottom={0}
                left={-16}
                margin="auto"
                position="absolute"
                top={0}
                variant="ghost"
              >
                <Icon as={HiArrowLeft} boxSize={6} color="primary.800" />
              </IconButton>
            </Link>
          )}
          <Text display="flex" mb={[5, 8]} textStyle="h2">
            {t('title')}
          </Text>
        </Box>
        <LoginMethodSelect
          onMethodChange={handleLoginMethodChange}
          selectedMethod={selectedMethod}
        />
        <LoginMethodComponent onSuccess={onLoginSuccess} />
      </Box>
    </LoggedOutLayout>
  );
};
