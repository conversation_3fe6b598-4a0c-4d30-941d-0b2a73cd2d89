import { Box, Button, HStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  PasswordInput,
  PasswordStrengthWidget,
  TextInput,
} from 'shared/components';
import { useShowError } from 'shared/hooks/alerts';
import { useInviteHash } from 'shared/hooks/merchant';
import { usePasswordSignUp } from 'shared/hooks/signup';
import { region } from 'shared/lib';
import { PASSWORD_LENGTH } from 'shared/types';
import { APP_LANGUAGE_ABBR, AppRegions } from 'shared/utils';
import { z } from 'zod';

const PasswordSignUpSchema = z.object({
  firstName: z.string().min(1, 'required'),
  lastName: z.string().min(1, 'required'),
  password: z.string().min(1, 'signup:password-quality-hint'),
  pin: z.string().min(1, 'required'),
});

type PasswordSignUpSchemaType = z.infer<typeof PasswordSignUpSchema>;

type Props = {
  email: string;
  onSuccess: () => void;
};

export const PasswordSignUp = ({ onSuccess, email }: Props) => {
  const { t, i18n } = useTranslation(['login', 'signup', 'common']);

  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.LV && i18n.language === APP_LANGUAGE_ABBR.ru;

  const { isLoading, validationErrors, signUpWithPassword } = usePasswordSignUp(
    {
      onSuccess,
    },
  );
  const showError = useShowError();

  const inviteHash = useInviteHash();

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<PasswordSignUpSchemaType>({
    resolver: zodResolver(PasswordSignUpSchema),
  });

  const password = watch('password');

  const getPinErrorMsg = (): string => {
    if (errors.pin?.message) {
      return t(`common:forms.${errors.pin.message}`);
    }

    if (validationErrors?.pin) {
      return t(`common:errors.pin.${validationErrors.pin}`);
    }

    return '';
  };

  const onSubmit = useCallback(
    async (data: PasswordSignUpSchemaType) => {
      if (!email || !inviteHash) {
        showError(t('common:alert.unknown-error'));
        return;
      }

      await signUpWithPassword({
        variables: {
          inviteHash,
          email,
          pin: data.pin,
          firstName: data.firstName,
          lastName: data.lastName,
          password: data.password,
          confirmPassword: data.password,
        },
      });
    },
    [signUpWithPassword, email, inviteHash, showError, t],
  );

  return (
    <Box as="form" onSubmit={handleSubmit(onSubmit)} width="100%">
      <HStack mb={[0, 3]} gap={3} w="100%">
        <TextInput
          data-cy="terminal-personal-information-first-name"
          label={t('signup:personal-information.first-name.label')}
          {...register('firstName')}
          error={
            errors.firstName?.message
              ? t(`common:forms.${errors.firstName.message}`)
              : undefined
          }
          isDisabled={false}
        />
        <TextInput
          data-cy="terminal-personal-information-last-name"
          label={t('signup:personal-information.last-name.label')}
          {...register('lastName')}
          error={
            errors.lastName?.message
              ? t(`common:forms.${errors.lastName.message}`)
              : undefined
          }
          isDisabled={false}
        />
      </HStack>
      <TextInput
        autoComplete="pin"
        data-cy="pin-input"
        disabled={isLoading}
        label={t(
          isLatvianRegionRussianSpecificCopy ? 'pin-label-lv' : 'pin-label',
        )}
        mb={[0, 3]}
        {...register('pin')}
        error={getPinErrorMsg()}
      />
      <Controller
        control={control}
        name="password"
        render={({ field: { onChange, ...rest } }) => (
          <PasswordInput
            autoComplete="password"
            data-cy="password-input"
            error={
              errors.password?.message
                ? t(errors.password.message, { amount: PASSWORD_LENGTH })
                : undefined
            }
            hint={t('signup:password-quality-hint', {
              amount: PASSWORD_LENGTH,
            })}
            disabled={isLoading}
            label={t('password-label')}
            mb={[7, 9]}
            onChange={(e) => {
              onChange((e.target as HTMLInputElement).value.trim());
            }}
            {...rest}
          />
        )}
      />

      <PasswordStrengthWidget mb={[7, 9]} password={password} />
      <Button
        colorScheme={ColorSchemes.PRIMARY}
        data-cy="password-login-button"
        loading={isLoading}
        type="submit"
        width="full"
      >
        {t('signup:signup-btn')}
      </Button>
    </Box>
  );
};
