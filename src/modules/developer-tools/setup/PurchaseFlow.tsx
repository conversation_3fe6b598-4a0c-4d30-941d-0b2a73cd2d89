import { Box, Circle, List, Text } from '@chakra-ui/react';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

const steps = Object.freeze([1, 2, 3]);

export const PurchaseFlow: FC = () => {
  const { t } = useTranslation('dev-tools');

  return (
    <Box mb={[12, null, 16]}>
      <Text mb={[3, 2]} textStyle="h4">
        {t('flow.title')}
      </Text>
      <List.Root>
        {steps.map((step) => (
          <List.Item
            display="flex"
            key={step}
            mb={[step !== steps[steps.length - 1] ? 4 : 0]}
            position="relative"
          >
            <Circle
              bg="primary.600"
              left={[0, null, '-32px']}
              position="absolute"
              size={5}
              top="2px"
            >
              <Text
                color="white"
                fontSize=".875rem"
                textStyle="body2-highlight"
              >
                {step}
              </Text>
            </Circle>
            <Text ml={['32px', null, 0]} textStyle="body1">
              {t(`flow.step-${step}`)}
            </Text>
          </List.Item>
        ))}
      </List.Root>
    </Box>
  );
};
