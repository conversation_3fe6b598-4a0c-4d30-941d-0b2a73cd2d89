import {
  Box,
  Button,
  Center,
  Spinner,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import { type FC, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { TextSubtextInput } from 'shared/components';
import { useShowMessageWithCallToActionElement } from 'shared/hooks/alerts';
import { useMerchantDetails } from 'shared/hooks/merchant';

import type { MerchantDataUpdateSchemaType } from './../../settings/merchant/MerchantInfoModal';
import {
  MerchantUrlsModal,
  type MerchantUrlsSchemaType,
} from './MerchantUrlsModal';

export const MerchantUrls: FC = () => {
  const { t } = useTranslation('dev-tools');
  const { loading, data } = useMerchantDetails();

  const { open, onOpen, onClose } = useDisclosure();
  const showMessage = useShowMessageWithCallToActionElement();

  const merchantUrlsFields: MerchantUrlsSchemaType = useMemo(() => {
    return {
      cancel_url: data?.merchant?.cancel_url ?? '',
      home_url: data?.merchant?.home_url ?? '',
      notification_url: data?.merchant?.notification_url ?? '',
      return_url: data?.merchant?.return_url ?? '',
    };
  }, [data?.merchant]);

  const requiredValues: MerchantDataUpdateSchemaType = useMemo(() => {
    return {
      name: data?.merchant?.name ?? '',
      registry_code: data?.merchant?.registry_code ?? '',
      iban: data?.merchant?.iban ?? '',
      beneficiary_name: data?.merchant?.beneficiary_name ?? '',
      email: data?.merchant?.email ?? '',
      phone: data?.merchant?.phone ?? '',
      address: data?.merchant?.address ?? '',
    };
  }, [data?.merchant]);

  const onUpdateSuccess = useCallback(
    (undoChanges?: () => void) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      undoChanges && onClose();
      showMessage({
        message: undoChanges
          ? t('notifications.merchant-urls-updated')
          : t('notifications.merchant-urls-changes-reverted'),
        callToAction: undoChanges ?? undoChanges,
        callToActionLabel: t('undo'),
      });
    },
    [t, showMessage, onClose],
  );

  if (loading) {
    return (
      <Center height="10rem" width="100%">
        <Spinner boxSize="2rem" color="primary.800" />
      </Center>
    );
  }

  return (
    <Box>
      <VStack mb={[8, null, 12]} gap={6}>
        <TextSubtextInput
          data-cy="merchant-url-home"
          inputMode="text"
          isDisabled={true}
          label={t('urls.home')}
          name="url_home"
          subText={t('urls.home-subtext')}
          value={merchantUrlsFields.home_url}
        />
        <TextSubtextInput
          data-cy="merchant-url-return"
          inputMode="text"
          isDisabled={true}
          label={t('urls.return')}
          name="url_return"
          subText={t('urls.return-subtext')}
          value={merchantUrlsFields.return_url}
        />
        <TextSubtextInput
          data-cy="merchant-url-notification"
          inputMode="text"
          isDisabled={true}
          label={t('urls.notification')}
          name="url_notification"
          subText={t('urls.notification-subtext')}
          value={merchantUrlsFields.notification_url}
        />
        <TextSubtextInput
          data-cy="merchant-url-cancel"
          inputMode="text"
          isDisabled={true}
          label={t('urls.cancel')}
          name="url_cancel"
          subText={t('urls.cancel-subtext')}
          value={merchantUrlsFields.cancel_url}
        />
      </VStack>
      <Button
        colorScheme={ColorSchemes.PRIMARY}
        data-cy="merchant-urls-open-modal-btn"
        onClick={onOpen}
        width="full"
      >
        {t('urls.open-modal-btn')}
      </Button>
      {!!open && (
        <MerchantUrlsModal
          defaultValues={merchantUrlsFields}
          isOpen
          onClose={onClose}
          onSuccess={onUpdateSuccess}
          requiredValues={requiredValues}
        />
      )}
    </Box>
  );
};
