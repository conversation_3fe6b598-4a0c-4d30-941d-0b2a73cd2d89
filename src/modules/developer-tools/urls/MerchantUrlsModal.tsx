import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
} from '@chakra-ui/modal';
import { Button, VStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback } from 'react';
import { type DefaultValues, type FieldName, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ModalCloseButton,
  TextInput,
} from 'shared/components';
import { useShowError } from 'shared/hooks/alerts';
import { useUpdateMerchant } from 'shared/hooks/merchant';
import { z } from 'zod';

import type { MerchantDataUpdateSchemaType } from './../../settings/merchant/MerchantInfoModal';

const MerchantUrlsSchema = z.object({
  cancel_url: z.string().min(1, 'required'),
  home_url: z.string().min(1, 'required'),
  notification_url: z.string().min(1, 'required'),
  return_url: z.string().min(1, 'required'),
});

export type MerchantUrlsSchemaType = z.infer<typeof MerchantUrlsSchema>;

type Props = {
  isOpen: boolean;
  defaultValues: DefaultValues<MerchantUrlsSchemaType>;
  requiredValues: MerchantDataUpdateSchemaType;
  onSuccess: (callToAction?: () => void) => void;
  onClose: () => void;
};

export const MerchantUrlsModal = ({
  isOpen,
  defaultValues,
  onClose,
  onSuccess,
  requiredValues,
}: Props) => {
  const { t } = useTranslation(['dev-tools', 'common']);
  const showError = useShowError();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<MerchantUrlsSchemaType>({
    defaultValues,
    resolver: zodResolver(MerchantUrlsSchema),
  });

  const onFieldValidationError = useCallback(
    (field: string) => {
      setError(
        field as FieldName<MerchantUrlsSchemaType>,
        { message: 'invalid-format' },
        { shouldFocus: true },
      );
    },
    [setError],
  );

  const { isLoading: isSubmitting, updateMerchant } = useUpdateMerchant({
    onFieldValidationError,
  });

  // revert urls changes
  const handleUrlsChangesUndo = useCallback(async () => {
    try {
      await updateMerchant({ ...defaultValues, ...requiredValues });
      onSuccess();
    } catch {
      showError("Can't perform URLs update .");
    }
  }, [updateMerchant, defaultValues, requiredValues, onSuccess, showError]);

  // save urls changes
  const onSubmit = useCallback(
    async (formValues: MerchantUrlsSchemaType) => {
      try {
        await updateMerchant({ ...formValues, ...requiredValues });
        onSuccess(handleUrlsChangesUndo);
      } catch {
        showError("Can't perform URLs update.");
      }
    },
    [
      updateMerchant,
      requiredValues,
      onSuccess,
      handleUrlsChangesUndo,
      showError,
    ],
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} scrollBehavior="inside">
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy="merchant-urls-modal-content"
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>{t('urls.modal.heading')}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={[4, 1]}>
          <VStack gap={1}>
            <TextInput
              autoFocus
              data-cy="merchant-urls-modal-home"
              inputMode="text"
              label={t('urls.home')}
              subText={t('urls.home-subtext')}
              {...register('home_url')}
              error={
                errors.home_url
                  ? t(`common:forms.${errors.home_url?.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
            <TextInput
              data-cy="merchant-urls-modal-return"
              inputMode="text"
              label={t('urls.return')}
              subText={t('urls.return-subtext')}
              {...register('return_url')}
              error={
                errors.return_url
                  ? t(`common:forms.${errors.return_url?.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
            <TextInput
              data-cy="merchant-urls-modal-notification"
              inputMode="text"
              label={t('urls.notification')}
              subText={t('urls.notification-subtext')}
              {...register('notification_url')}
              error={
                errors.notification_url
                  ? t(`common:forms.${errors.notification_url?.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
            <TextInput
              data-cy="merchant-urls-modal-cancel"
              inputMode="text"
              label={t('urls.cancel')}
              subText={t('urls.cancel-subtext')}
              {...register('cancel_url')}
              error={
                errors.cancel_url
                  ? t(`common:forms.${errors.cancel_url?.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
          </VStack>
        </ModalBody>

        <ModalFooter
          borderTop={['1px solid', 'none']}
          borderTopColor="neutral.150"
        >
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            display={['none', 'inline-flex']}
            disabled={isSubmitting}
            mr={[0, 3]}
            mt={[3, 0]}
            onClick={onClose}
          >
            {t('common:forms.cancel')}
          </Button>
          <ButtonWithLoader
            data-cy="merchant-settings-modal-submit"
            disabled={isSubmitting}
            loading={isSubmitting}
            type="submit"
          >
            {t('urls.modal.save')}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
