import { HStack, Icon, IconButton, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { IoMdCheckmarkCircle, IoMdClose } from 'react-icons/io';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

type Props = {
  onCloseClick: () => void;
};

// TODO: remove after 6 months
export const FinanceNewViewInfo = ({ onCloseClick }: Props) => {
  const { t } = useTranslation(LocizeNamespaces.FINANCE);

  return (
    <HStack
      alignItems="flex-start"
      bg="gray.50"
      borderRadius={4}
      p={['12px 16px']}
      gap={2.5}
    >
      <Icon as={IoMdCheckmarkCircle} boxSize={5} color="primary.700" />
      <Text flex="1">{t(LocizeFinanceKeys.NEW_VIEW_TEXT)}</Text>
      <IconButton
        aria-label="close-new-view-info"
        border="none"
        colorScheme="blackAlpha"
        onClick={onCloseClick}
        size="xs"
        variant="outline"
      >
        <Icon as={IoMdClose} boxSize={5} color="neutral.700" />
      </IconButton>
    </HStack>
  );
};
