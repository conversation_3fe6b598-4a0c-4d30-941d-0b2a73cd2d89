import { useReactiveVar } from '@apollo/client';
import { Stack } from '@chakra-ui/react';
import { saveAs } from 'file-saver';
import { useTransactionsData } from 'modules/finance/transactions-table/TransactionsTable.hooks';
import type React from 'react';
import { useCallback, useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ButtonWithLoader,
  DateRange,
  useFormattedDateRange,
} from 'shared/components';
import { useTimestamp } from 'shared/components/date-range';
import { useShowError } from 'shared/hooks/alerts';
import { GlobalStateContext } from 'shared/hooks/state';

import { BOOK_TYPES, METHODS } from '../TransactionsTable.constants';
import { transactionsTableState } from '../TransactionsTable.utils';

const restAPIendpoint = process.env.REACT_APP_API_ENDPOINT?.replace(
  '/graphql',
  '',
);

export const DateSelectBlock: React.FC = () => {
  const tableState = useReactiveVar(transactionsTableState);
  const { t, i18n } = useTranslation('finance');
  const { formattedDate } = useFormattedDateRange(tableState.dateRange);
  const { merchantId } = useContext(GlobalStateContext);
  const timestampFrom = useTimestamp(tableState.dateRange.from || new Date());
  const timestampTo = useTimestamp(tableState.dateRange.to || new Date());
  const merchantRefOrCustomerName = tableState.search;
  const { loading, data } = useTransactionsData({
    page: 1,
  });
  const showError = useShowError();

  const [isDownloading, setIsDownloading] = useState(false);

  const handleTransactionsDownload = useCallback(async () => {
    setIsDownloading(true);
    const options: RequestInit = {
      credentials: 'include',
      headers: { mode: 'same-origin' },
    };
    const url = new URL(
      `${restAPIendpoint}/merchant/${merchantId}/transactions/export`,
    );
    const params = new URLSearchParams();

    params.set('timestamp_from', timestampFrom.toString());
    params.set('timestamp_to', timestampTo.toString());
    params.set('lang', i18n.language);
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    merchantRefOrCustomerName?.trim() &&
      params.set('merchant_ref_or_customer_name', merchantRefOrCustomerName);
    for (const method of METHODS) {
      params.append('methods[]', method);
    }
    for (const bookType of BOOK_TYPES) {
      params.append('book_types[]', bookType);
    }

    url.search = params.toString();

    try {
      const response = await fetch(url.toString(), options);
      const blob = await response?.blob();

      saveAs(blob, 'ESTO_transactions.xlsx');
    } catch (error) {
      if (error instanceof Error) {
        showError(error.message);
      }
    } finally {
      setIsDownloading(false);
    }
  }, [
    merchantId,
    timestampFrom,
    timestampTo,
    i18n.language,
    merchantRefOrCustomerName,
    showError,
  ]);

  return (
    <Stack
      alignItems="flex-start"
      direction={['column', 'row']}
      gap={[0, 6]}
      width="100%"
    >
      <DateRange
        mb={[5, 0]}
        onChange={(range) => {
          if (range && typeof range === 'object' && 'from' in range) {
            transactionsTableState({
              ...tableState,
              page: 1,
              dateRange: range,
            });
          }
        }}
        placement="bottom-end"
        value={tableState.dateRange}
        w={['100%', null, null, null, '17.5rem']}
      />

      <ButtonWithLoader
        disabled={isDownloading}
        loading={isDownloading}
        noOpacity={Boolean(
          (!data?.transactions?.data?.length && loading) ||
            (data?.transactions?.data?.length && !loading),
        )}
        onClick={handleTransactionsDownload}
        size="sm"
        w={['100%', null, null, null, 'fit-content']}
      >
        {t('download')} {formattedDate}
      </ButtonWithLoader>
    </Stack>
  );
};
