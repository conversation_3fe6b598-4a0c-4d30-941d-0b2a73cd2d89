import { useReactiveVar } from '@apollo/client';
import { type BoxProps, Button } from '@chakra-ui/react';
import { Td as ChakraTd, Tr } from '@chakra-ui/table';
import type { PropsWithChildren } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { Loader } from 'shared/components';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import { TRANSACTIONS_TABLE_LIMIT_BY_PAGE } from '../TransactionsTable.constants';
import { useTransactionsData } from '../TransactionsTable.hooks';
import { transactionsTableState } from '../TransactionsTable.utils';
import { TransactionClientName } from './TransactionClientName';

type Props = {
  page: number;
  portalElement: HTMLDivElement | null;
};

export const TransactionsCollectionByPage = ({
  page,
  portalElement,
}: Props) => {
  const { t, i18n } = useTranslation(LocizeNamespaces.FINANCE);
  const tableState = useReactiveVar(transactionsTableState);
  const { loading, data } = useTransactionsData({
    page,
  });

  if (loading) {
    return (
      portalElement && createPortal(<Loader height="5rem" />, portalElement)
    );
  }

  if (!data?.transactions?.data?.length) {
    return null;
  }

  const moreTransactionsToLoad =
    data.transactions.total -
    (page - 1) * TRANSACTIONS_TABLE_LIMIT_BY_PAGE -
    data.transactions.data.length;
  const isLastRenderedPage = tableState.page === page;
  const shouldRenderLoadMore = isLastRenderedPage && moreTransactionsToLoad > 0;

  return (
    <>
      {data?.transactions?.data.map((transactionItem) => {
        if (!transactionItem) {
          return null;
        }

        const {
          id,
          from_amount,
          to_amount,
          application_trashed,
          transaction,
          book,
        } = transactionItem;

        const amountData = from_amount - to_amount;

        return (
          <Tr key={id}>
            <Td>
              {application_trashed?.merchant_data?.reference ||
                application_trashed?.invoice_reference_nr ||
                '-'}
            </Td>
            <Td>
              <TransactionClientName applicationTrashed={application_trashed} />
            </Td>
            <Td
              color={amountData < 0 ? 'red.700' : 'neutral.must'}
              textAlign="right"
            >
              {`${amountData.toLocaleString(i18n.language, {
                maximumFractionDigits: 2,
                minimumFractionDigits: 2,
              })}€`}
            </Td>
            <Td>{t(`method.${transaction?.method}`)}</Td>
            <Td>{t(`type.${book?.type}`)}</Td>
            <Td>{transaction?.payment?.description ?? '-'}</Td>
            <Td>{transaction?.transaction_date}</Td>
          </Tr>
        );
      })}
      {!!shouldRenderLoadMore &&
        !!portalElement &&
        createPortal(
          <Button
            data-cy="transactions-load-more"
            onClick={() =>
              transactionsTableState({
                ...tableState,
                page: tableState.page + 1,
              })
            }
            width="full"
          >
            {t(LocizeFinanceKeys.TABLE_LOAD_MORE_DATA_LABEL)}
          </Button>,
          portalElement,
        )}
    </>
  );
};

const Td = ({ children, ...props }: PropsWithChildren<BoxProps>) => (
  <ChakraTd
    borderBottom="1px solid"
    borderColor="gray.200"
    color="gray.800"
    pl={{ base: 0, lg: 3 }}
    pr={5}
    py={{ base: 3, md: 2 }}
    fontSize="sm"
    whiteSpace="nowrap"
    {...(props as any)}
  >
    {children}
  </ChakraTd>
);
