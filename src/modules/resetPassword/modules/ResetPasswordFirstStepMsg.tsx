import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
} from '@chakra-ui/accordion';
import { List } from '@chakra-ui/react';
import { Box, type BoxProps, Button, Text } from '@chakra-ui/react';
import type { FC } from 'react';
import { Trans, useTranslation } from 'react-i18next';

const btnStyles: BoxProps = {
  textStyle: 'body2-highlight',
  color: 'primary.800',
  padding: 0,
  _hover: { backgroundColor: 'none', color: 'primary.700' },
  _active: { backgroundColor: 'none' },
  _focus: { backgroundColor: 'none', outline: 0 },
  sx: { fontSize: '0.875rem', height: 'auto' },
};

type Props = {
  email: string;
  goToChangeEmail: () => void;
  resendLink: () => void;
  isLinkResent: boolean;
};

export const ResetPasswordFirstStepMsg: FC<Props> = ({
  email,
  goToChangeEmail: handleGoToEmail,
  resendLink: handleResendLink,
  isLinkResent,
}) => {
  const { t } = useTranslation('reset-password');

  return (
    <>
      <Text data-cy="first-step-msg-title" mb={[5, 8]} textStyle="h2">
        {t(`first-step-msg.title-${isLinkResent ? 2 : 1}`)}
      </Text>
      <Box
        alignItems="center"
        backgroundColor="primary.100"
        borderRadius="4px"
        color="primary.900"
        display="flex"
        mb={10}
        px={3}
        py={2}
      >
        <Text data-cy="first-step-description" textStyle="body2">
          <Trans
            components={{ strong: <strong /> }}
            i18nKey="first-step-msg.description"
            ns="reset-password"
            values={{ email }}
          />
        </Text>
      </Box>

      <Accordion allowToggle>
        <AccordionItem border="none" mb={-3}>
          <AccordionButton
            {...btnStyles}
            data-cy="first-step-accordion-btn"
            mb={3}
            w="auto"
          >
            <AccordionIcon fontSize="1.5rem" mr={2} />
            <Box flex="1" textAlign="left" textStyle="body2-highlight">
              {t('first-step-msg.accordion-btn')}
            </Box>
          </AccordionButton>

          <AccordionPanel bgColor="neutral.50" borderRadius="4px" px={5} py={4}>
            <List.Root gap={4} textStyle="body2" listStyleType="disc" pl={4}>
              <List.Item>
                <Trans
                  components={{ strong: <strong /> }}
                  i18nKey="first-step-msg.recom-1"
                  ns="reset-password"
                  values={{ email }}
                />
                <br />
                <Button
                  onClick={handleGoToEmail}
                  {...btnStyles}
                  data-cy="not-your-email-btn"
                >
                  {t('first-step-msg.not-your-email')}
                </Button>
              </List.Item>
              <List.Item>
                <Text>{t('first-step-msg.recom-2')}</Text>
              </List.Item>
              <List.Item>
                <Text>{t('first-step-msg.recom-3')}</Text>
              </List.Item>
              {!isLinkResent && (
                <List.Item>
                  <Text>
                    {t('first-step-msg.recom-4')}{' '}
                    <Button
                      onClick={handleResendLink}
                      {...btnStyles}
                      data-cy="send-another-email-btn"
                    >
                      {t('first-step-msg.send-another-email')}
                    </Button>
                  </Text>
                </List.Item>
              )}
            </List.Root>
          </AccordionPanel>
        </AccordionItem>
      </Accordion>
    </>
  );
};
