import { ChakraProvider } from '@chakra-ui/react';
import {
  type PropsWithChildren,
  type ReactNode,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { getSystem } from 'shared/chakra-theme';
import i18n from 'shared/lib/i18n';
import type { AvailableLanguage } from 'shared/types';

export const AppChakraProvider = ({ children }: PropsWithChildren) => {
  const [lang, setLang] = useState(i18n.language);

  const system = useMemo(
    () => getSystem({ language: lang as AvailableLanguage }),
    [lang],
  );

  useEffect(() => {
    i18n.on('languageChanged', setLang);

    return () => {
      i18n.off('languageChanged', setLang);
    };
  }, []);

  return <ChakraProvider value={system}>{children}</ChakraProvider>;
};

export const withChakra = (component: () => ReactNode) => () => {
  return <AppChakraProvider>{component()}</AppChakraProvider>;
};
